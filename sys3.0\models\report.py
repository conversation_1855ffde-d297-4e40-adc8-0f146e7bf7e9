"""
报告数据模型
定义风险评估报告的结构和方法
"""

from datetime import datetime
from typing import Dict, List, Any, Optional, TypedDict, Union
from pathlib import Path
import re
import unicodedata

from .constants import RiskLevel
from utils.exceptions import DataException

class RiskItem(TypedDict, total=False):
    """风险项目"""
    hazard_id: str               # 隐患ID
    risk_level: RiskLevel        # 风险等级
    hazard_description: str      # 隐患描述
    location: str                # 地点
    timestamp: str               # 时间戳
    violation: bool              # 是否违规
    law_clause: Optional[str]    # 命中法规条款
    evidence_chain: str          # 证据链说明
    law_reason: Optional[str]    # 法规命中原因
    weibo_url: Optional[str]     # 原微博链接
    evidence_urls: List[str]     # 证据图片URL列表

class Report:
    """报告类，表示一个完整的安全分析报告"""
    
    def __init__(self, data: Dict[str, Any] | None = None):
        """
        初始化报告
        
        Args:
            data: 报告数据，默认为空字典
        """
        data = data or {}
        
        # 基本信息
        self.report_id = data.get("report_id", "")
        self.title = data.get("title", "")
        self.created_at = data.get("created_at", datetime.now().isoformat())
        self.updated_at = data.get("updated_at", datetime.now().isoformat())
        self.author = data.get("author", "系统")
        self.version = data.get("version", "1.0")
        
        # 报告内容
        self.summary = data.get("summary", "")
        self.introduction = data.get("introduction", "")
        self.methodology = data.get("methodology", "")
        self.findings = data.get("findings", [])
        self.analysis = data.get("analysis", {})
        self.conclusions = data.get("conclusions", "")
        self.recommendations = data.get("recommendations", [])
        
        # 风险分组数据 - 新增支持
        self.risks_by_level = data.get("risks_by_level", {})
        
        # 元数据
        self.metadata = data.get("metadata", {})
        self.tags = data.get("tags", [])
        self.related_reports = data.get("related_reports", [])
        
        # 保存原始数据
        self._raw_data = data
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            "report_id": self.report_id,
            "title": self.title,
            "created_at": self.created_at,
            "updated_at": datetime.now().isoformat(),
            "author": self.author,
            "version": self.version,
            "summary": self.summary,
            "introduction": self.introduction,
            "methodology": self.methodology,
            "findings": self.findings,
            "analysis": self.analysis,
            "conclusions": self.conclusions,
            "recommendations": self.recommendations,
            "risks_by_level": self.risks_by_level,
            "metadata": self.metadata,
            "tags": self.tags,
            "related_reports": self.related_reports
        }
    
    def save(self, file_path: Union[str, Path]) -> None:
        """
        保存报告
        
        Args:
            file_path: 文件路径
        """
        # 在方法内部导入，避免循环导入
        from utils.data_service import data_service
        
        # 将报告转换为字典
        report_dict = self.to_dict()
        
        # 保存报告
        data_service.save_json(report_dict, file_path)

    @classmethod
    def load(cls, file_path: Union[str, Path]) -> 'Report':
        """
        从文件加载报告
        
        Args:
            file_path: 文件路径
            
        Returns:
            Report: 加载的报告
        """
        # 在方法内部导入，避免循环导入
        from utils.data_service import data_service
        
        # 加载报告数据
        try:
            report_data = data_service.load_json(file_path)
            return cls(report_data)
        except Exception as e:
            raise DataException(f"加载报告失败: {str(e)}", file_path=str(file_path))
    
    def to_markdown(self) -> str:
        """
        转换为Markdown格式，支持按风险等级分组
        
        Returns:
            str: Markdown格式的报告
        """
        # 构建Markdown文本
        md = []
        
        # 标题和元信息
        md.append(f"# {self.title}\n")
        md.append(f"**报告ID**: {self.report_id}  ")
        md.append(f"**创建时间**: {self.created_at}  ")
        md.append(f"**版本**: {self.version}\n\n")
        
        # 摘要
        if self.summary:
            md.append("## 摘要\n")
            md.append(f"{self.summary}\n\n")
        
        # 按风险等级分组展示
        if self.risks_by_level:
            # 风险等级顺序
            risk_levels = ["必须立即处理", "建议整改优化", "提醒关注观察", "信息不完整"]
            
            # 遍历风险等级
            for level in risk_levels:
                risks = self.risks_by_level.get(level, [])
                if not risks:
                    continue
                    
                md.append(f"## 风险等级：{level}\n")
                md.append(f"共 {len(risks)} 项\n\n")
                
                # 遍历该等级下的所有风险
                for i, risk in enumerate(risks):
                    hazard_id = risk.get("hazard_id", f"HZ-{i+1}")
                    description = risk.get("hazard_description", risk.get("description", "null"))

                    md.append(f"### {i+1}. 风险事件：{hazard_id}（{description}）\n\n")

                    # 完整的基本信息 - 确保所有字段都显示
                    md.append(f"**隐患ID**: {hazard_id}\n\n")
                    md.append(f"**风险等级**: {level}\n\n")
                    md.append(f"**时间**: {risk.get('timestamp', risk.get('date', 'null'))}\n\n")
                    md.append(f"**地点**: {risk.get('location', 'null')}\n\n")
                    md.append(f"**来源**: {risk.get('author', 'null')}\n\n")
                    md.append(f"**描述**: {description}\n\n")

                    # 违规信息
                    violation = risk.get("violation", False)
                    md.append(f"**是否违规**: {'是' if violation else '否'}\n\n")

                    # 法规信息 - 完整显示
                    md.append(f"**相关法规**: {risk.get('law_clause', 'null')}\n\n")
                    md.append(f"**法规说明**: {risk.get('law_reason', 'null')}\n\n")

                    # 图谱路径 - 支持多行显示
                    graph_path = risk.get('graph_path', 'null')
                    if graph_path and graph_path != 'null' and '\n' in graph_path:
                        md.append(f"**图谱路径**:\n{graph_path}\n\n")
                    else:
                        md.append(f"**图谱路径**: {graph_path}\n\n")

                    # 推理链条说明
                    md.append(f"**推理链条说明**: {risk.get('evidence_chain', 'null')}\n\n")

                    # 微博地址
                    weibo_url = risk.get('weibo_url', risk.get('url', ''))
                    md.append(f"**微博地址**: {weibo_url or 'null'}\n\n")
        
        # 如果没有按风险等级分组的数据，但有findings，则使用旧格式
        elif self.findings:
            md.append("## 主要发现\n")
            for i, finding in enumerate(self.findings):
                md.append(f"### 发现 {i+1}: {finding.get('title', '')}\n")
                md.append(f"{finding.get('description', '')}\n")
                if finding.get('severity'):
                    md.append(f"**严重程度**: {finding.get('severity')}\n")
                if finding.get('evidence'):
                    md.append(f"**证据**: {finding.get('evidence')}\n")
                md.append("\n")
        
        # 结论
        if self.conclusions:
            md.append("## 结论\n")
            md.append(f"{self.conclusions}\n\n")
        
        # 建议
        if self.recommendations:
            md.append("## 建议\n")
            for i, rec in enumerate(self.recommendations):
                md.append(f"### 建议 {i+1}: {rec.get('title', '')}\n")
                md.append(f"{rec.get('description', '')}\n")
                if rec.get('priority'):
                    md.append(f"**优先级**: {rec.get('priority')}\n")
                md.append("\n")
        
        return "\n".join(md)
    
    def save_markdown(self, output_path: Union[str, Path]) -> None:
        """
        保存为Markdown文件
        
        Args:
            output_path: 输出文件路径
        """
        # 生成Markdown内容
        md_content = self.to_markdown()
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
    
    def normalize_text_fields(self) -> None:
        """
        规范化文本字段，处理特殊字符和格式
        """
        # 规范化摘要
        if self.summary:
            self.summary = self._normalize_text(self.summary)
        
        # 规范化引言
        if self.introduction:
            self.introduction = self._normalize_text(self.introduction)
        
        # 规范化方法论
        if self.methodology:
            self.methodology = self._normalize_text(self.methodology)
        
        # 规范化结论
        if self.conclusions:
            self.conclusions = self._normalize_text(self.conclusions)
        
        # 规范化发现
        for finding in self.findings:
            if 'description' in finding:
                finding['description'] = self._normalize_text(finding['description'])
        
        # 规范化建议
        for rec in self.recommendations:
            if 'description' in rec:
                rec['description'] = self._normalize_text(rec['description'])
    
    def _normalize_text(self, text: str) -> str:
        """
        规范化文本，处理特殊字符和格式
        
        Args:
            text: 输入文本
            
        Returns:
            str: 规范化后的文本
        """
        if not text:
            return ""
        
        # 替换连续多个换行为两个换行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 规范化Unicode字符
        text = unicodedata.normalize('NFC', text)
        
        return text
