"""
微博搜索API接口

提供程序化方式使用微博爬虫的接口

使用说明:
1. 配置爬虫:
   在 tools/weibo_search/settings.py 中配置所有参数：
   - KEYWORD_LIST: 关键词列表
   - START_DATE: 开始日期
   - END_DATE: 结束日期
   - REGION: 地区
   - 其他配置项

2. 初始化API:
   api = WeiboSearchAPI()

3. 运行爬虫:
   task_id = api.run()  # 使用settings.py中的配置

4. 获取任务状态:
   status = api.get_task_status(task_id)

5. 获取任务结果:
   result = api.get_task_result(task_id)
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent  # 指向sys3.0目录
sys.path.insert(0, str(ROOT_DIR))

# 导入日志模块
from utils.logger import get_logger

# 导入数据服务
from utils.data_service import data_service
from utils.exceptions import DataException

class WeiboSearchAPI:
    """微博搜索API
    
    提供程序化方式使用微博爬虫
    """
    def __init__(self, config=None):
        """初始化API
        
        Args:
            config: 微博爬虫配置，包括：
                - cookie: 微博Cookie（可选）
                - proxies: 代理设置（可选）
                - request_headers: 请求头设置（可选）
        """
        # 设置默认配置
        self.config = config or {}
        
        # 初始化日志
        self.logger = get_logger("weibo_api")
        
        # 设置数据和状态目录
        from utils.data_service import data_service
        self.status_dir = str(data_service.status_dir)
        self.data_dir = str(data_service.raw_data_dir / "weibo")
        
        self.logger.info("微博搜索API初始化完成")
    
    def set_cookie(self, cookie):
        """设置微博cookie
        
        Args:
            cookie: 微博cookie字符串
        """
        if cookie:
            self.config['cookie'] = cookie
            self.logger.info("已设置微博cookie")
    
    def run(self, task_id=None, async_mode=False):
        """运行爬虫

        Args:
            task_id: 任务ID，默认为当前时间戳
            async_mode: 是否异步运行

        注意: 微博爬虫的所有配置（关键词、时间范围、地区等）都通过settings.py文件配置，
        不再接受任何运行时参数

        Returns:
            str: 任务ID或结果文件路径
        """
        # 任务ID设置
        task_id = task_id or data_service.generate_task_id()
        
        # 准备爬虫参数 - 只保留任务ID和状态目录
        args = {
            'task_id': task_id,
            'status_dir': self.status_dir
        }
        
        # 构建爬虫命令
        cmd = [sys.executable, "-m", "scrapy", "crawl", "search"]
                
        # 添加参数
        for key, value in args.items():
            if isinstance(value, (list, dict)):
                value = json.dumps(value, ensure_ascii=False)
            cmd.extend(["-a", f"{key}={value}"])
        
        # 设置JSON输出 - 使用标准化的路径格式
        task_dir = Path(self.data_dir) / task_id
        output_path = task_dir / "weibo_data.json"
        task_dir.mkdir(parents=True, exist_ok=True)
        cmd.extend(["-s", f"JSON_OUTPUT_PATH={str(output_path)}"])
        
        # 创建图片和视频存储目录（如果settings中设置了下载媒体）
        images_store = task_dir / 'images'
        files_store = task_dir / 'videos'
        
        # 创建目录
        images_store.mkdir(parents=True, exist_ok=True)
        files_store.mkdir(parents=True, exist_ok=True)
        
        # 修改工作目录为项目根目录，确保能正确找到模块
        project_root = Path(__file__).parent.parent.parent
        
        # 设置环境变量，确保Scrapy能找到正确的设置模块
        env = os.environ.copy()
        env['PYTHONPATH'] = str(project_root)
        env['SCRAPY_SETTINGS_MODULE'] = 'tools.weibo_search.settings'
        
        self.logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 执行爬虫
        if async_mode:
            # 异步模式
            process = subprocess.Popen(cmd, cwd=str(project_root), env=env)
            return task_id
        else:
            # 同步模式
            try:
                subprocess.run(cmd, cwd=str(project_root), env=env, check=True)
                return str(output_path)
                
            except subprocess.CalledProcessError as e:
                self.logger.error(f"爬虫执行失败: {e}")
                # 如果输出文件不存在，则创建空的结果文件
                if not os.path.exists(output_path):
                    self.logger.info(f"API创建空的结果文件: {output_path}")
                    with open(output_path, 'w', encoding='utf-8') as f:
                        json.dump([], f)
                
                # 抛出异常，由调用者处理
                raise RuntimeError(f"爬虫执行失败: {e}")
    
    def get_task_status(self, task_id):
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        status = data_service.get_task_status(task_id)
        if status and "phases" in status and "collect" in status["phases"]:
            return status["phases"]["collect"]
        return {"status": "unknown"}
    
    def get_task_result_file(self, task_id):
        """获取任务结果文件路径
        
        Args:
            task_id: 任务ID
            
        Returns:
            str: 结果文件路径
        """
        # 使用标准化的路径格式
        return str(Path(self.data_dir) / task_id / "weibo_data.json")
    
    def get_task_result(self, task_id):
        """获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Dict]: 微博数据列表
        """
        file_path = self.get_task_result_file(task_id)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundException, json.JSONDecodeError) as e:
            self.logger.error(f"读取任务结果失败: {e}")
            return [] 
