"""
系统状态检查模块
提供系统状态检查和诊断功能
"""

import logging
from typing import Dict, Any, Tuple

# 导入配置服务
from config.config import config_service

def check_system_status(skip_db_check=False) -> Tuple[bool, Dict[str, Any]]:
    """
    检查系统状态
    
    检查配置、LLM、数据目录、工具等组件的状态
    
    Args:
        skip_db_check: 是否跳过数据库检查
    
    Returns:
        tuple: (整体状态, 状态详情)
    """
    logger = logging.getLogger("system")
    
    # 显示系统运行模式
    logger.info("\n" + "=" * 60)
    logger.info("= 系统运行中，将从网络收集数据")
    logger.info("=" * 60 + "\n")
    
    status = {
        "config": {"status": "unknown", "message": ""},
        "llm": {"status": "unknown", "message": ""},
        "data_dirs": {"status": "unknown", "message": ""}
    }
    
    # 只有在不跳过数据库检查时才添加neo4j状态项
    if not skip_db_check:
        status["neo4j"] = {"status": "unknown", "message": ""}

    # 添加GPU状态检查
    status["gpu"] = {"status": "unknown", "message": ""}
    
    # 检查配置
    try:
        # 简单的配置检查
        config_service.load_config()
        status["config"] = {"status": "ok", "message": "配置加载成功"}
    except Exception as e:
        status["config"] = {"status": "error", "message": f"配置加载失败: {str(e)}"}
    
    # 检查LLM
    try:
        # 使用配置服务获取LLM配置
        llm_config = config_service.get_config("llm", as_dict=True)
        debug_mode = config_service.is_debug_mode()



        # 导入LLM客户端
        from tools.llm_client import LLMClient

        # 检查API密钥是否存在
        if not llm_config.get("api_key") and not debug_mode:
            status["llm"] = {"status": "error", "message": "缺少LLM API密钥，请在env文件中设置LLM_API_KEY"}
        else:
            try:
                # 在调试模式下，如果没有API密钥，则跳过实际初始化
                if debug_mode and not llm_config.get("api_key"):
                    status["llm"] = {"status": "warning", "message": "调试模式：LLM API密钥缺失，但在调试模式下允许继续"}
                else:
                    # 尝试初始化客户端，但不实际调用API
                    llm = LLMClient(llm_config)
                    # 获取LLM实例进行验证
                    llm.get_llm()
                    status["llm"] = {"status": "ok", "message": f"LLM配置有效，提供商: {llm_config.get('provider')}"}
            except Exception as e:
                status["llm"] = {"status": "error", "message": f"LLM初始化失败: {str(e)}"}
    except Exception as e:
        status["llm"] = {"status": "error", "message": f"LLM客户端导入失败: {str(e)}"}
    
    # 检查数据目录 - 使用配置服务
    try:
        # 检查必要的数据目录是否存在
        raw_data_dir = config_service.get_path("raw_data_dir")
        if raw_data_dir.exists():
            status["data_dirs"] = {"status": "ok", "message": "数据目录已准备就绪"}
        else:
            # 尝试创建目录
            config_service.ensure_directories()
            status["data_dirs"] = {"status": "ok", "message": "数据目录已创建"}
    except Exception as e:
        status["data_dirs"] = {"status": "error", "message": f"数据目录检查失败: {str(e)}"}
    
    # 检查Neo4j连接
    if not skip_db_check:
        try:
            from tools.graph_engine.core.engine import GraphEngine
            engine = GraphEngine()
            engine.query("MATCH (n) RETURN count(n) as count LIMIT 1")
            status["neo4j"] = {"status": "ok", "message": "Neo4j连接正常"}
        except Exception as e:
            status["neo4j"] = {"status": "error", "message": f"Neo4j连接失败: {str(e)}"}
    else:
        logger.info("根据用户设置，跳过Neo4j数据库检查")

    # 检查GPU状态
    try:
        import torch
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            status["gpu"] = {
                "status": "ok",
                "message": f"GPU可用: {device_name}"
            }
        else:
            status["gpu"] = {"status": "warning", "message": "GPU不可用，使用CPU模式"}
    except Exception as e:
        status["gpu"] = {"status": "error", "message": f"GPU状态检查失败: {str(e)}"}

    # 打印状态
    logger.info("系统状态检查结果:")
    for component, info in status.items():
        if info["status"] == "ok":
            logger.info(f"- {component}: {info['status']} - {info['message']}")
        elif info["status"] == "warning":
            logger.warning(f"- {component}: {info['status']} - {info['message']}")
        else:
            logger.error(f"- {component}: {info['status']} - {info['message']}")
    
    # 返回整体状态，warning也视为可接受
    overall_status = all(info["status"] in ["ok", "warning"] for info in status.values())
    return overall_status, status 