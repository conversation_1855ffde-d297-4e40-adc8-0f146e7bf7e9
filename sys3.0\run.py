#!/usr/bin/env python

import os
import sys
from pathlib import Path
import time
import platform
import signal

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 禁用 OpenTelemetry 自动检测
os.environ["OTEL_PYTHON_DISABLED"] = "true"
os.environ["DISABLE_TELEMETRY"] = "true"

# 设置Neo4j日志级别，减少调试信息
import logging
neo4j_debug = os.environ.get("NEO4J_DEBUG", "false").lower() == "true"
neo4j_log_level = logging.DEBUG if neo4j_debug else logging.WARNING
logging.getLogger("neo4j").setLevel(neo4j_log_level)
logging.getLogger("neo4j.io").setLevel(neo4j_log_level)
logging.getLogger("neo4j.pool").setLevel(neo4j_log_level)

# 导入日志配置
from utils.logger import setup_logging

# 配置日志系统
setup_logging({
    'level': os.environ.get("LOG_LEVEL", "INFO"),
    'console': True,
    'file': str(ROOT_DIR / "logs" / "system.log")
})

# 导入配置服务
from config.config import config_service

# 优化初始化顺序：先加载配置（包括.env文件），确保目录存在
config_service.load_config()
config_service.ensure_directories()

# 智能自动加载图谱数据
def auto_load_graph_data():
    """智能自动加载图谱数据到Neo4j数据库，支持变化检测"""
    try:
        from tools.graph_engine.smart_auto_loader import smart_auto_load_graph_data
        print("🔍 智能检查图谱数据状态...")
        success = smart_auto_load_graph_data(force_reload=False)
        if success:
            print("✅ 图谱数据库准备就绪")
        else:
            print("⚠️ 图谱数据加载失败，系统将继续运行但图谱功能可能受限")
    except Exception as e:
        print(f"⚠️ 图谱数据智能加载出错: {e}")
        print("系统将继续运行但图谱功能可能受限")

# 执行图谱数据自动加载
auto_load_graph_data()

# 设置LLM环境变量
print("🔧 初始化LLM环境...")
config_service.setup_llm_env()

# 设置数据目录环境变量
os.environ['RAW_DATA_DIR'] = str(config_service.get_path("raw_data_dir"))
os.environ['STATUS_DIR'] = str(config_service.get_path("status_dir"))

# 导入其他必要模块
print("📦 加载系统模块...")
from utils.cli import parse_args
from utils.system_check import check_system_status
from utils.data_service import data_service
from utils.logger import get_logger

# 获取系统日志记录器
logger = get_logger("system")

# 导入控制器智能体
from agents.controller import ControllerAgent

# 不要在这里导入其他智能体，避免提前初始化
os.environ["LITELLM_SKIP_INIT"] = "1"  # 禁用 LiteLLM 自动初始化

def main():
    """
    主函数，系统入口点
    """
    # 解析命令行参数
    try:
        args = parse_args()
    except ValueError as e:
        logger.error(f"\n错误: {str(e)}")
        return
    
    # 生成任务ID，如果没有提供
    task_id = args.task_id or data_service.generate_task_id()

    # 打印系统信息
    logger.info(f"系统信息: Python {sys.version}, OS: {platform.system()}")
    logger.info(f"CrewAI框架版本已加载，系统将使用CrewAI执行工作")

    # 如果只是检查系统状态
    if args.mode == "check":
        logger.info("执行系统状态检查..")
        status, status_details = check_system_status(skip_db_check=args.skip_db_check)
        if status:
            logger.info("\n系统状态检查通过，可以正常运行")
        else:
            failed_components = [comp for comp, info in status_details.items() if info["status"] != "ok"]
            if failed_components:
                error_msgs = [f"{comp}: {status_details[comp]['message']}" for comp in failed_components]
                logger.error(f"\n系统状态检查失败。以下组件存在问题:\n- " + "\n- ".join(error_msgs))
            else:
                logger.error("\n系统状态检查存在未知问题，请检查日志")
        return

    try:
            # 创建控制器智能体
            logger.info("初始化控制器智能体..")
            controller = ControllerAgent(verbose=config_service.get_config("system.debug_mode", False))
            
            # 准备任务参数
            # 注意：微博爬虫相关参数（topic, location, time_range, max_posts, keywords）
            # 不会传递给微博爬虫，微博爬虫只使用settings.py中的配置
            task_params = {
                "topic": args.topic,  # 仅用于其他智能体
                "location": args.location,  # 仅用于其他智能体
                "time_range": args.time_range,  # 仅用于其他智能体
                "max_posts": args.max_posts,  # 仅用于其他智能体
                "keywords": args.keywords or [],  # 仅用于其他智能体
                "input_file": args.input,
                "report_path": args.report_path
            }
            
            # 验证参数规则
            param_rules = config_service.get_config("system.run_mode.param_rules", {})
            if args.mode in param_rules:
                required_params = param_rules[args.mode].get("required", [])
                for param in required_params:
                    if param not in task_params or task_params[param] is None:
                        logger.error(f"模式 {args.mode} 需要参数 {param}")
                        return
            
            # 检查依赖关系
            # 如果提供了输入文件，则跳过依赖关系检查
            if not args.input and args.mode in config_service.get_config("system.run_mode.dependencies", {}):
                dependencies = config_service.get_config("system.run_mode.dependencies")[args.mode]
                deps_str = "、".join(dependencies)
                logger.error(f"模式 {args.mode} 依赖于{deps_str}的输出，请提供输入文件或先运行依赖的模式")
                return
            
            # 准备任务输入
            task_input = {
                "mode": args.mode,
                "params": task_params,
                "task_id": task_id
            }
            
            # 执行任务
            logger.info(f"开始执行任务，任务ID: {task_id}, 模式: {args.mode}")
            start_time = time.time()

            # 执行任务
            result = controller.execute(task_input)

            # 记录执行时间
            execution_time = time.time() - start_time
            logger.info(f"任务执行完成，耗时: {execution_time:.2f} 秒")

            # 保存结果到数据服务
            data_service.update_task_status(task_id, "controller", "completed", result)

            # 输出结果摘要
            if result.get("status") == "success":
                logger.info("\n" + "=" * 60)
                logger.info("= 任务执行成功")
                logger.info(f"= 任务ID: {task_id}")
                logger.info(f"= 执行时间: {execution_time:.2f} 秒")

                # 根据不同任务模式输出不同的结果摘要
                if args.mode == "full" and "reporting" in result and "report_file" in result["reporting"]:
                    logger.info(f"= 报告已生成: {result['reporting']['report_file']}")
                elif args.mode == "report" and "report_file" in result:
                    logger.info(f"= 报告已生成: {result['report_file']}")

                logger.info("=" * 60 + "\n")
            else:
                error_msg = result.get("error", "未知错误")
                logger.error("\n" + "=" * 60)
                logger.error("= 任务执行失败")
                logger.error(f"= 错误: {error_msg}")
                logger.error("=" * 60 + "\n")

    except Exception as e:
        # 计算执行时间，如果start_time未定义则执行时间为0
        execution_time = time.time() - start_time if 'start_time' in locals() else 0
        logger.exception(f"任务执行失败，耗时: {execution_time:.2f} 秒")
        logger.error("\n" + "=" * 60)
        logger.error("= 任务执行失败")
        logger.error(f"= 错误: {str(e)}")
        logger.error("=" * 60 + "\n")


if __name__ == "__main__":
    # 设置信号处理
    def signal_handler(sig, frame):
        """信号处理函数"""
        _ = sig, frame  # 忽略未使用的参数
        logger.warning("收到终止信号，正在退出...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 执行主函数
    sys.exit(main()) 
