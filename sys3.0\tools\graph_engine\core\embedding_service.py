"""
语义向量化服务
提供文本向量化和语义相似度计算功能，支持Neo4j 6.0向量索引
"""

import os
import numpy as np
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging
from sentence_transformers import SentenceTransformer
import torch

from utils.logger import LoggerMixin
from utils.exceptions import SystemException


class EmbeddingService(LoggerMixin):
    """语义向量化服务类"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        初始化向量化服务
        
        Args:
            model_path: 模型路径，如果为None则使用默认路径
        """
        super().__init__()
        
        # 设置默认模型路径
        if model_path is None:
            current_dir = Path(__file__).parent.parent.parent.parent
            model_path = current_dir / "models" / "paraphrase-multilingual-MiniLM-L12-v2"
        
        self.model_path = str(model_path)
        self.model = None
        self.embedding_dim = 384  # MiniLM-L12-v2的向量维度
        
        # 初始化模型
        self._load_model()
    
    def _load_model(self) -> None:
        """加载向量化模型"""
        try:
            self.log_info(f"正在加载向量化模型: {self.model_path}")
            
            # 检查模型路径是否存在
            if not Path(self.model_path).exists():
                raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
            
            # 加载模型
            self.model = SentenceTransformer(self.model_path)

            # 设置设备
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model = self.model.to(device)

            self.log_info(f"向量化模型加载成功，使用设备: {device}")
            
        except Exception as e:
            error_msg = f"加载向量化模型失败: {str(e)}"
            self.log_error(error_msg, e)
            raise SystemException(error_msg)
    
    def encode_text(self, text: str) -> List[float]:
        """
        将单个文本转换为向量
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 文本向量
        """
        try:
            if not text or not text.strip():
                self.log_warning("输入文本为空，返回零向量")
                return [0.0] * self.embedding_dim
            
            # 文本预处理
            text = text.strip()
            
            # 向量化
            embedding = self.model.encode(text, convert_to_tensor=False)
            
            # 确保返回Python list
            if isinstance(embedding, np.ndarray):
                embedding = embedding.tolist()
            
            return embedding
            
        except Exception as e:
            error_msg = f"文本向量化失败: {str(e)}"
            self.log_error(error_msg, e)
            raise SystemException(error_msg)
    
    def encode_batch(self, texts: List[str], batch_size: int = None) -> List[List[float]]:
        """
        批量将文本转换为向量，支持GPU优化

        Args:
            texts: 文本列表
            batch_size: 批处理大小，None时自动根据GPU内存调整

        Returns:
            List[List[float]]: 向量列表
        """
        try:
            if not texts:
                return []

            # 过滤空文本
            valid_texts = [text.strip() for text in texts if text and text.strip()]

            if not valid_texts:
                self.log_warning("所有输入文本为空")
                return [[0.0] * self.embedding_dim] * len(texts)

            # 自动调整批处理大小
            if batch_size is None:
                device = "cuda" if torch.cuda.is_available() else "cpu"
                if device == "cuda":
                    # 根据GPU内存动态调整批处理大小
                    try:
                        # 简单的内存检测
                        total_memory = torch.cuda.get_device_properties(0).total_memory
                        free_memory_gb = total_memory / (1024**3)

                        if free_memory_gb > 8:
                            batch_size = 64
                        elif free_memory_gb > 4:
                            batch_size = 32
                        else:
                            batch_size = 16
                    except:
                        batch_size = 16
                else:
                    batch_size = 16  # CPU模式使用较小批处理

            # 如果文本数量小于批处理大小，直接处理
            if len(valid_texts) <= batch_size:
                embeddings = self.model.encode(
                    valid_texts,
                    convert_to_tensor=False,
                    show_progress_bar=False
                )

                # 确保返回Python list
                if isinstance(embeddings, np.ndarray):
                    embeddings = embeddings.tolist()

                return embeddings

            # 分批处理大量文本
            self.log_info(f"使用批处理大小: {batch_size}, 处理文本数量: {len(valid_texts)}")

            all_embeddings = []
            for i in range(0, len(valid_texts), batch_size):
                batch_texts = valid_texts[i:i + batch_size]

                # GPU内存优化
                if torch.cuda.is_available() and i > 0 and i % (batch_size * 4) == 0:
                    torch.cuda.empty_cache()  # 定期清理缓存

                batch_embeddings = self.model.encode(
                    batch_texts,
                    convert_to_tensor=False,
                    show_progress_bar=len(valid_texts) > 100,  # 大批量时显示进度
                    batch_size=min(batch_size, len(batch_texts))
                )

                # 确保返回Python list
                if isinstance(batch_embeddings, np.ndarray):
                    batch_embeddings = batch_embeddings.tolist()

                all_embeddings.extend(batch_embeddings)

            # 最终清理
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            return all_embeddings

        except Exception as e:
            error_msg = f"批量文本向量化失败: {str(e)}"
            self.log_error(error_msg, e)
            # GPU内存清理
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            raise SystemException(error_msg)
    
    def compute_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            embedding1: 第一个向量
            embedding2: 第二个向量
            
        Returns:
            float: 余弦相似度 (0-1)
        """
        try:
            # 转换为numpy数组
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            
            # 确保结果在[0, 1]范围内
            return max(0.0, min(1.0, float(similarity)))
            
        except Exception as e:
            error_msg = f"相似度计算失败: {str(e)}"
            self.log_error(error_msg, e)
            return 0.0
    
    def get_embedding_dimension(self) -> int:
        """获取向量维度"""
        return self.embedding_dim
    
    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.model is not None


# 创建全局实例
_embedding_service = None

def get_embedding_service() -> EmbeddingService:
    """获取全局向量化服务实例"""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = EmbeddingService()
    return _embedding_service
