# 建筑生命全周期安全隐患预测系统依赖包
# 基于CrewAI框架的智能体系统
#
# 环境要求: Python 3.13.5 (推荐3.11+)
# 基于safety环境的实际版本 - 最后更新: 2025-07-20
#
# 安装说明:
# 1. 创建conda环境: conda create -n safety python=3.13.5
# 2. 激活环境: conda activate safety
# 3. 安装依赖: pip install -r requirements.txt

# 核心框架 - 基于safety环境的实际版本
crewai==0.148.0
pydantic==2.10.3
langchain==0.3.26
langchain-community==0.3.27
langchain-huggingface==0.3.0
langchain-openai==0.3.28

# 数据处理 - 基于safety环境的实际版本
pandas==2.2.3
numpy==1.26.4
python-dotenv==1.1.0

# 机器学习和向量化 - 基于safety环境的实际版本
sentence-transformers==5.0.0
transformers==4.53.2
# PyTorch with CUDA support - install with:
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
torch>=2.5.1
faiss-gpu==1.7.2

# 数据库连接 - 基于safety环境的实际版本
neo4j==5.28.1
pymongo==4.13.2

# 网络请求和爬虫 - 基于safety环境的实际版本
requests==2.32.3
aiohttp==3.11.10
scrapy==2.12.0
selenium==4.34.2
httpx==0.28.1

# 日志和工具 - 基于safety环境的实际版本
loguru==0.7.3
click==8.1.8

# LLM相关 - 基于safety环境的实际版本
litellm==1.72.6
openai==1.97.0

# 文本处理 - 基于safety环境的实际版本
jieba==0.42.1
beautifulsoup4==4.12.3
lxml==5.3.0

# 数据科学 - 基于safety环境的实际版本
scikit-learn==1.6.1
matplotlib==3.10.0
seaborn==0.13.2

# 异步处理 - 基于safety环境的实际版本
asyncio-mqtt==0.16.2
aiofiles==24.1.0

# 配置和验证 - 基于safety环境的实际版本
jsonschema==4.23.0
marshmallow==3.26.1
pyyaml==6.0.2
toml==0.10.2

# 时间处理 - 基于safety环境的实际版本
python-dateutil==2.9.0.post0
pytz==2024.1

# 文件处理 - 基于safety环境的实际版本
openpyxl==3.1.5
xlsxwriter==3.2.5
chardet==5.2.0

# 图像处理 - 基于safety环境的实际版本
Pillow==11.1.0

# 加密和安全 - 基于safety环境的实际版本
cryptography==45.0.5

# 系统监控 - 基于safety环境的实际版本
psutil==5.9.0

# 数据处理和工具 - 基于safety环境的实际版本
tqdm==4.67.1
colorama==0.4.6
tabulate==0.9.0

# 网络和HTTP工具 - 基于safety环境的实际版本
urllib3==2.5.0
certifi==2025.7.14

# 测试工具（开发环境）- 基于safety环境的实际版本
pytest==8.3.4
pytest-asyncio==1.1.0
pytest-cov==6.2.1

# 代码质量 - 基于safety环境的实际版本
black==24.10.0
flake8==7.1.1
isort==6.0.1
