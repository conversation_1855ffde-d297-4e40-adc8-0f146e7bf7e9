"""
收集器智能体模块
负责从各种来源收集数据，基于CrewAI框架实现
"""

# 标准库导入
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

# 第三方库导入
from crewai import Agent, Task, TaskOutput, tools
from pydantic import BaseModel, Field

# 本地应用导入
from .base_agent import BaseAgent
from config.config import config_service
from tools.weibo_search.api import WeiboSearchAPI
from utils.data_service import data_service
from utils.exceptions import TaskException, NetworkException, DataException, safe_execute

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ROOT_DIR))

def create_search_weibo_tool(weibo_api):
    """创建微博搜索工具函数"""

    @tools.tool("搜索微博内容")
    def search_weibo(keywords: str = "建筑安全", region: str = "全部") -> str:
        """
        搜索微博内容，使用系统配置的关键词和日期范围进行搜索

        Args:
            keywords: 搜索关键词，多个关键词用逗号分隔
            region: 搜索地区

        Returns:
            str: 搜索结果的JSON字符串
        """
        # 生成任务ID
        task_id = data_service.generate_task_id()

        # 调用微博搜索API - 只传递任务ID
        if weibo_api:
            result_file = weibo_api.run(
                task_id=task_id,
                async_mode=False
            )
        else:
            # 如果没有API实例，创建一个临时的
            temp_weibo_api = WeiboSearchAPI()
            result_file = temp_weibo_api.run(
                task_id=task_id,
                async_mode=False
            )

        return json.dumps({
            "task_id": task_id,
            "result_file": str(result_file) if result_file else None,
            "status": "started"
        })

    return search_weibo

class CollectorAgent(BaseAgent):
    """
    收集器智能体
    负责从微博等来源收集数据
    基于CrewAI框架实现
    """
    
    def __init__(
        self,
        name: str = "数据收集者",
        role: str = "建筑安全数据收集专家",
        goal: str = "从微博等社交媒体平台收集与建筑安全相关的信息",
        backstory: Optional[str] = None,
        llm_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False
    ):
        """初始化收集器智能体"""
        backstory = backstory or "专业的建筑安全数据收集专家，负责从社交媒体平台收集建筑安全相关信息。"

        # 创建微博工具
        weibo_api = WeiboSearchAPI()
        search_weibo_tool = create_search_weibo_tool(weibo_api)

        # 初始化基类
        super().__init__(
            name=name,
            role=role,
            goal=goal,
            backstory=backstory,
            llm_config=llm_config,
            verbose=verbose,
            tools=[search_weibo_tool]
        )

        # 将微博API存储在私有属性中，避免Pydantic字段冲突
        self._weibo_api = weibo_api
    
    @safe_execute
    def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行微博数据收集任务"""
        # 获取任务参数
        task_id = task_input.get('task_id') or data_service.generate_task_id()

        self.log_info(f"开始执行微博数据收集任务: {task_id}")

        # 更新任务状态为运行中
        data_service.update_task_status(task_id, "collect", "running", {
            "data_source": "weibo",
            "start_time": datetime.now().isoformat()
        })

        # 执行数据收集
        result = self._collect_from_weibo(task_input)

        # 更新任务状态为完成
        data_service.update_task_status(task_id, "collect", "completed", {
            "data_source": "weibo",
            "end_time": datetime.now().isoformat()
        })

        return result
    
    def __str__(self) -> str:
        return f"CollectorAgent({self.name})"
    
    def _collect_from_weibo(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        从微博收集数据，输出标准WeiboItem格式

        Args:
            task_input: 任务输入参数
                - task_id: 任务ID

        Returns:
            Dict[str, Any]: 收集结果，包含标准化的WeiboItem列表
        """
        task_id = task_input.get('task_id') or data_service.generate_task_id()

        # 执行微博搜索
        try:
            # 调用微博搜索API - 只传递任务ID
            result_file = self._weibo_api.run(
                task_id=task_id,
                async_mode=False
            )

            # 等待任务完成
            status = self._weibo_api.get_task_status(task_id)
            while status.get('status') == 'running':
                self.log_info(f"微博搜索任务正在运行，已收集 {status.get('collected', 0)} 条微博")
                time.sleep(5)
                status = self._weibo_api.get_task_status(task_id)

            # 获取结果文件路径
            result_file = Path(self._weibo_api.get_task_result_file(task_id))

            # 检查结果文件 - 避免重复创建
            if not result_file.exists():
                # 如果结果文件不存在，则创建一个空的JSON文件
                self.log_info(f"API返回的结果文件不存在，创建空结果文件: {result_file}")
                # 确保目录存在
                result_file.parent.mkdir(parents=True, exist_ok=True)
                # 创建空的JSON文件
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump([], f)

            # 读取原始微博数据
            with open(result_file, 'r', encoding='utf-8') as f:
                raw_weibo_data = json.load(f)

            # 提取微博列表数据
            if isinstance(raw_weibo_data, dict) and 'posts' in raw_weibo_data:
                weibo_posts = raw_weibo_data['posts']
            elif isinstance(raw_weibo_data, list):
                weibo_posts = raw_weibo_data
            else:
                self.log_error(f"未知的微博数据格式: {type(raw_weibo_data)}")
                weibo_posts = []

            # 转换为标准WeiboItem格式
            standardized_data = self._convert_to_weibo_items(weibo_posts)

            # 保存标准化数据 - 只保存一份到系统标准位置
            try:
                # 保存到系统标准的原始数据位置
                data_service.save_task_data(task_id, "raw", standardized_data)
                self.log_info(f"原始数据已保存到任务: {task_id}")

                # 获取保存的文件路径用于返回
                standardized_file = data_service.get_task_path(task_id, "raw")
            except Exception as e:
                self.log_error(f"保存标准化数据失败: {str(e)}")
                raise TaskException(f"保存标准化数据失败: {str(e)}", task_id=task_id)

            self.log_info(f"微博数据收集完成，共收集 {len(raw_weibo_data)} 条微博，标准化为 {len(standardized_data)} 条WeiboItem")

            return {
                'status': 'success',
                'task_id': task_id,
                'data': standardized_data,
                'data_file': str(standardized_file),
                'count': len(standardized_data)
            }

        except Exception as e:
            self.log_error(f"微博数据收集失败: {str(e)}")
            return {
                "status": "error",
                "error": f"微博数据收集失败: {str(e)}",
                "task_id": task_id,
                "data": []
            }
    
    def _convert_to_weibo_items(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        将原始微博数据转换为标准WeiboItem格式

        Args:
            raw_data: 原始微博数据列表

        Returns:
            List[Dict[str, Any]]: 标准化的WeiboItem列表
        """
        standardized_items = []

        for item in raw_data:
            try:
                # 构建标准WeiboItem
                weibo_item = {
                    "weibo_id": str(item.get('id', '')),
                    "weibo_url": item.get('weibo_url', '') or item.get('article_url', '') or f"https://weibo.com/detail/{item.get('id', '')}",
                    "timestamp": item.get('created_at', ''),
                    "location": item.get('location', ''),
                    "author": item.get('screen_name', ''),
                    "content": item.get('text', ''),
                    "images": item.get('pics', []) if item.get('pics') else [],
                    "videos": [item.get('video_url')] if item.get('video_url') else []
                }

                # 只添加有效的微博项（至少要有ID和内容）
                if weibo_item["weibo_id"] and weibo_item["content"]:
                    standardized_items.append(weibo_item)
                else:
                    self.log_warning(f"跳过无效微博项: ID={weibo_item['weibo_id']}, 内容长度={len(weibo_item['content'])}")

            except Exception as e:
                self.log_error(f"转换微博项失败: {str(e)}, 原始数据: {item}")
                continue

        self.log_info(f"成功转换 {len(standardized_items)}/{len(raw_data)} 条微博为标准格式")
        return standardized_items

    def check_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        检查任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务状态
        """
        return data_service.get_task_status(task_id, "collect")
