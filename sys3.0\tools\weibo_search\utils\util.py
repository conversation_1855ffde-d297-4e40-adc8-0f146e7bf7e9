"""
微博工具函数模块

提供各种微博爬虫相关的工具函数
"""

import sys
import os
import re
import json
import logging
import csv
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple

from utils.exceptions import DataException
from utils.logger import get_logger, LoggerMixin
from .region import region_dict
from utils.data_service import data_service

# 创建日志记录器，这里不使用全局logger，而是在需要时创建局部logger
# 删除全局logger定义

class WeiboUtils(LoggerMixin):
    """微博工具类"""
    
    @staticmethod
    def convert_weibo_type(weibo_type):
        """将微博类型转换成字符串
        
        Args:
            weibo_type (int): 微博类型代码
                - 0: 全部微博
                - 1: 原创微博
                - 2: 热门微博
                - 3: 关注人微博
                - 4: 认证用户微博
                - 5: 媒体微博
                - 6: 观点微博
        
        Returns:
            str: 用于URL参数的微博类型字符串
        """
        type_map = {
            0: "&typeall=1",
            1: "&scope=ori",
            2: "&xsort=hot",
            3: "&atten=1",
            4: "&vip=1",
            5: "&category=4",
            6: "&viewpoint=1"
        }
        return type_map.get(weibo_type, "&typeall=1")


    @staticmethod
    def convert_contain_type(contain_type):
        """将包含类型转换成字符串
        
        Args:
            contain_type (int): 内容包含类型代码
                - 0: 全部微博
                - 1: 包含图片
                - 2: 包含视频
                - 3: 包含音乐
                - 4: 包含短链接
        
        Returns:
            str: 用于URL参数的内容包含类型字符串
        """
        contain_map = {
            0: "&suball=1",
            1: "&haspic=1",
            2: "&hasvideo=1",
            3: "&hasmusic=1",
            4: "&haslink=1"
        }
        return contain_map.get(contain_type, "&suball=1")


    @classmethod
    def get_keyword_list(cls, keyword_list):
        """获取关键词列表
        
        Args:
            keyword_list (str或list): 可以是关键词列表，也可以是包含关键词的文件路径
        
        Returns:
            list: 关键词列表
        """
        utils = cls()
        
        # 如果是字符串，可能是文件路径
        if isinstance(keyword_list, str):
            # 检查是否是文件路径
            if os.path.exists(keyword_list):
                try:
                    with open(keyword_list, 'r', encoding='utf-8') as f:
                        # 尝试作为JSON加载
                        try:
                            data = json.load(f)
                            if isinstance(data, list):
                                return data
                            elif isinstance(data, dict) and "keywords" in data:
                                return data["keywords"]
                        except json.JSONDecodeError:
                            # 不是JSON，按行读取
                            return [line.strip() for line in f if line.strip()]
                except Exception as e:
                    utils.log_error(f"读取关键词文件失败: {e}")
                    return [keyword_list]  # 返回原始字符串作为关键词
            else:
                # 不是文件路径，作为单个关键词返回
                return [keyword_list]
        elif isinstance(keyword_list, list):
            # 已经是列表，直接返回
            return keyword_list
        else:
            # 其他类型，转换为字符串后作为单个关键词返回
            return [str(keyword_list)]


    @staticmethod
    def get_regions(region):
        """根据区域筛选条件返回符合要求的region

        Args:
            region (list或str): 地区名称列表或单个地区名称，None表示所有地区

        Returns:
            dict: 符合条件的地区编码字典
        """
        # 如果region为None或空，返回所有地区
        if not region:
            return region_dict

        new_region = {}
        # 处理字符串转列表
        if isinstance(region, str):
            region = [region]

        # 检查是否包含"全部"，如果包含则返回所有地区
        if '全部' in region:
            return region_dict

        # 获取对应的编码
        for key in region:
            if region_dict.get(key):
                new_region[key] = region_dict[key]

        # 如果指定了地区但没有找到任何匹配的地区，抛出错误
        if not new_region:
            raise ValueError(f"未找到指定的地区: {region}")

        return new_region


    @staticmethod
    def standardize_date(created_at):
        """标准化微博发布时间
        
        将微博发布时间标准化为"yyyy-mm-dd hh:mm"格式
        
        Args:
            created_at (str): 原始时间字符串，如"2分钟前"、"今天 13:01"、"05-11"等
            
        Returns:
            str: 标准化后的时间字符串，格式为"yyyy-mm-dd hh:mm"
        """
        if "刚刚" in created_at:
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M")
        elif "秒" in created_at:
            second = created_at[:created_at.find("秒")]
            second = timedelta(seconds=int(second))
            created_at = (datetime.now() - second).strftime("%Y-%m-%d %H:%M")
        elif "分钟" in created_at:
            minute = created_at[:created_at.find("分钟")]
            minute = timedelta(minutes=int(minute))
            created_at = (datetime.now() - minute).strftime("%Y-%m-%d %H:%M")
        elif "小时" in created_at:
            hour = created_at[:created_at.find("小时")]
            hour = timedelta(hours=int(hour))
            created_at = (datetime.now() - hour).strftime("%Y-%m-%d %H:%M")
        elif "今天" in created_at:
            today = datetime.now().strftime('%Y-%m-%d')
            created_at = today + ' ' + created_at[2:]
        elif '年' not in created_at:
            year = datetime.now().strftime("%Y")
            month = created_at[:2]
            day = created_at[3:5]
            time = created_at[6:]
            created_at = year + '-' + month + '-' + day + ' ' + time
        else:
            year = created_at[:4]
            month = created_at[5:7]
            day = created_at[8:10]
            time = created_at[11:]
            created_at = year + '-' + month + '-' + day + ' ' + time
        return created_at


    @staticmethod
    def str_to_time(text):
        """将字符串转换成时间类型
        
        Args:
            text (str): 日期字符串，格式为"yyyy-mm-dd"
            
        Returns:
            datetime: 转换后的日期对象
        """
        return datetime.strptime(text, '%Y-%m-%d')


    @classmethod
    def time_range_to_params(cls, time_range):
        """将时间范围转换为起始时间和结束时间
        
        Args:
            time_range (str): 时间范围，支持以下格式:
                - "7d": 表示最近7天
                - "30d": 表示最近30天
                - "自定义": 使用默认范围
                - "custom:yyyy-mm-dd-yyyy-mm-dd": 自定义时间范围，包含开始和结束时间
        
        Returns:
            tuple: (start_date, end_date) 开始日期和结束日期字符串，格式为"yyyy-mm-dd"
        """
        now = datetime.now()
        utils = cls()
        
        # 如果time_range为None或空字符串，返回错误
        if not time_range:
            raise ValueError("时间范围不能为空")
        
        # 处理自定义格式
        if time_range.startswith('custom:'):
            # 解析自定义时间范围
            date_range = time_range[7:]
            
            # 处理到现在的时间范围
            if '-now' in date_range:
                # 自定义开始时间到现在
                start_str = date_range.split('-now')[0]
                try:
                    # 尝试多种格式解析
                    try:
                        start_date = datetime.strptime(start_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            start_date = datetime.strptime(start_str, '%Y-%m-%d')
                        except ValueError:
                            # 如果解析失败，使用默认时间范围
                            start_date = now - timedelta(days=7)
                            
                    return start_date.strftime('%Y-%m-%d'), now.strftime('%Y-%m-%d')
                except Exception as e:
                    utils.log_warning(f"解析时间范围出错: {e}, 使用默认时间范围")
                    # 使用默认时间范围
                    start_date = now - timedelta(days=7)
                    return start_date.strftime('%Y-%m-%d'), now.strftime('%Y-%m-%d')
                    
            # 处理完整的时间范围
            elif '-' in date_range:
                try:
                    # 尝试解析完整时间范围
                    parts = date_range.split('-')
                    if len(parts) >= 2:
                        start_str = parts[0]
                        end_str = parts[1]
                        
                        # 尝试多种格式解析
                        try:
                            start_date = datetime.strptime(start_str, '%Y-%m-%d')
                        except ValueError:
                            # 尝试其他格式
                            try:
                                start_date = datetime.strptime(start_str, '%Y%m%d')
                            except ValueError:
                                # 如果解析失败，使用默认开始时间
                                start_date = now - timedelta(days=7)
                        
                        try:
                            end_date = datetime.strptime(end_str, '%Y-%m-%d')
                        except ValueError:
                            # 尝试其他格式
                            try:
                                end_date = datetime.strptime(end_str, '%Y%m%d')
                            except ValueError:
                                # 如果解析失败，使用当前时间
                                end_date = now
                        
                        # 确保开始时间不晚于结束时间
                        if start_date > end_date:
                            start_date, end_date = end_date, start_date
                            
                        return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')
                except Exception as e:
                    raise ValueError(f"解析时间范围出错: {e}")
        
        # 处理常用时间范围
        if time_range == 'today':
            # 今天
            return now.strftime('%Y-%m-%d'), now.strftime('%Y-%m-%d')
        elif time_range == 'yesterday':
            # 昨天
            yesterday = now - timedelta(days=1)
            return yesterday.strftime('%Y-%m-%d'), yesterday.strftime('%Y-%m-%d')
        elif time_range.endswith('d'):
            # 最近N天
            try:
                days = int(time_range[:-1])
                if days <= 0:
                    raise ValueError("天数必须大于0")
                start_date = now - timedelta(days=days)
                return start_date.strftime('%Y-%m-%d'), now.strftime('%Y-%m-%d')
            except ValueError as e:
                raise ValueError(f"无效的时间范围格式: {time_range}, {e}")
        
        # 如果没有匹配的格式，抛出错误
        raise ValueError(f"不支持的时间范围格式: {time_range}")


    @classmethod
    def save_task_status(cls, task_id, status, data, status_dir):
        """保存任务状态
        
        Args:
            task_id (str): 任务ID
            status (str): 任务状态
            data (dict): 状态数据
            status_dir (str或Path): 状态保存目录
        """
        utils = cls()
        status_file = Path(status_dir) / f"{task_id}.json"
        status_data = {
            "task_id": task_id,
            "status": status,
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": data
        }
        
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            utils.log_error(f"保存任务状态失败: {e}")
            return False

# 创建实例，方便直接调用
weibo_utils = WeiboUtils() 

# 将类方法暴露为模块级函数
def convert_weibo_type(weibo_type):
    """将微博类型转换成字符串
    
    Args:
        weibo_type (int): 微博类型代码
            - 0: 全部微博
            - 1: 原创微博
            - 2: 热门微博
            - 3: 关注人微博
            - 4: 认证用户微博
            - 5: 媒体微博
            - 6: 观点微博
    
    Returns:
        str: 用于URL参数的微博类型字符串
    """
    type_map = {
        0: "&typeall=1",
        1: "&scope=ori",
        2: "&xsort=hot",
        3: "&atten=1",
        4: "&vip=1",
        5: "&category=4",
        6: "&viewpoint=1"
    }
    return type_map.get(weibo_type, "&typeall=1")

def convert_contain_type(contain_type):
    """将包含类型转换成字符串
    
    Args:
        contain_type (int): 内容包含类型代码
            - 0: 全部微博
            - 1: 包含图片
            - 2: 包含视频
            - 3: 包含音乐
            - 4: 包含短链接
    
    Returns:
        str: 用于URL参数的内容包含类型字符串
    """
    contain_map = {
        0: "&suball=1",
        1: "&haspic=1",
        2: "&hasvideo=1",
        3: "&hasmusic=1",
        4: "&haslink=1"
    }
    return contain_map.get(contain_type, "&suball=1")

def get_keyword_list(keyword_list):
    """获取关键词列表
    
    Args:
        keyword_list (str或list): 可以是关键词列表，也可以是包含关键词的文件路径
    
    Returns:
        list: 关键词列表
    """
    return WeiboUtils.get_keyword_list(keyword_list)

def get_regions(region):
    """根据区域筛选条件返回符合要求的region
    
    Args:
        region (list或str): 地区名称列表或单个地区名称
        
    Returns:
        dict: 符合条件的地区编码字典
    """
    return WeiboUtils.get_regions(region)

def time_range_to_params(time_range):
    """将时间范围转换为起始时间和结束时间
    
    Args:
        time_range (str): 时间范围，支持以下格式:
            - "7d": 表示最近7天
            - "30d": 表示最近30天
            - "自定义": 使用默认范围
            - "custom:yyyy-mm-dd-yyyy-mm-dd": 自定义时间范围
    
    Returns:
        tuple: (start_date, end_date) 开始日期和结束日期字符串
    """
    return WeiboUtils.time_range_to_params(time_range)

def str_to_time(text):
    """将字符串转换成时间类型
    
    Args:
        text (str): 日期字符串，格式为"yyyy-mm-dd"
            
    Returns:
        datetime: 转换后的日期对象
    """
    return WeiboUtils.str_to_time(text)

def standardize_date(date_str):
    """标准化微博发布时间
    
    将微博的日期字符串转换为标准格式 (YYYY-MM-DD HH:MM)
    
    Args:
        date_str: 微博日期字符串，如"刚刚"、"5分钟前"、"今天 12:30"、"05月12日 12:30"等
        
    Returns:
        str: 标准格式的日期字符串
    """
    if "刚刚" in date_str:
        date_str = datetime.now().strftime("%Y-%m-%d %H:%M")
    elif "秒" in date_str:
        second = date_str[:date_str.find("秒")]
        second = timedelta(seconds=int(second))
        date_str = (datetime.now() - second).strftime("%Y-%m-%d %H:%M")
    elif "分钟" in date_str:
        minute = date_str[:date_str.find("分钟")]
        minute = timedelta(minutes=int(minute))
        date_str = (datetime.now() - minute).strftime("%Y-%m-%d %H:%M")
    elif "小时" in date_str:
        hour = date_str[:date_str.find("小时")]
        hour = timedelta(hours=int(hour))
        date_str = (datetime.now() - hour).strftime("%Y-%m-%d %H:%M")
    elif "今天" in date_str:
        today = datetime.now().strftime('%Y-%m-%d')
        date_str = today + ' ' + date_str[2:]
    elif '年' not in date_str:
        year = datetime.now().strftime("%Y")
        month = date_str[:2]
        day = date_str[3:5]
        time = date_str[6:]
        date_str = year + '-' + month + '-' + day + ' ' + time
    else:
        year = date_str[:4]
        month = date_str[5:7]
        day = date_str[8:10]
        time = date_str[11:]
        date_str = year + '-' + month + '-' + day + ' ' + time
    return date_str

def datetime_json_serializer(obj):
    """用于JSON序列化的自定义函数，处理datetime对象
    
    Args:
        obj: 需要序列化的对象
        
    Returns:
        str: 序列化后的字符串
    """
    if isinstance(obj, datetime):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def save_task_status(task_id, status, data, status_dir=None):
    """保存任务状态
    
    Args:
        task_id (str): 任务ID
        status (str): 任务状态
        data (dict): 状态数据
        status_dir (str或Path): 状态保存目录，如果为None则使用系统默认目录
    
    Returns:
        bool: 是否成功保存
    """
    try:
        # 使用数据服务更新任务状态
        data_service.update_task_status(task_id, "collect", status, data)
        return True
    except Exception as e:
        logger = get_logger("weibo_utils")
        logger.error(f"保存任务状态失败: {e}")
        return False