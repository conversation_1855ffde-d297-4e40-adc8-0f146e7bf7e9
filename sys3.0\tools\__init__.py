"""
工具集，包含系统中使用的所有工具，基于CrewAI框架实现
"""

from typing import Dict, Any, Optional

# 先定义工具接口基类
class ToolInterface:
    """工具接口基类，所有工具模块都应实现这个接口"""
    
    def __init__(self, config=None):
        """初始化工具
        
        Args:
            config: 工具配置
        """
        self.config = config or {}
    
    def execute(self, input_data):
        """执行工具功能
        
        Args:
            input_data: 输入数据
            
        Returns:
            执行结果
        """
        raise NotImplementedError("子类必须实现execute方法")

# 微博搜索工具
from .weibo_search import WeiboSearchAPI

# 法规检索引擎
from .law_rag_engine import search_laws, batch_search_laws, add_law_document, law_tools

# 知识图谱引擎
from .graph_engine import query_causal_paths, get_reasoning_context, graph_tools_list

# 导出所有工具列表
all_tools = law_tools + graph_tools_list

__all__ = [
    'ToolInterface',
    'WeiboSearchAPI',
    'search_laws', 'batch_search_laws', 'add_law_document', 'law_tools',
    'query_causal_paths', 'get_reasoning_context', 'graph_tools_list',
    'all_tools'
]
