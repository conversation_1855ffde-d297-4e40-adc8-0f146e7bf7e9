"""
控制器智能体模块
负责协调和管理整个建筑安全隐患预测流程，基于CrewAI框架实现
"""

import time
import json
import sys
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import traceback

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ROOT_DIR))

from crewai import Agent
from utils.exceptions import SystemException, ERR_SYSTEM, safe_execute
from utils.logger import LoggerMixin
from config.config import config_service
from utils.data_service import data_service
from utils.progress import create_progress_bar

from .base_agent import BaseAgent
from .collector import CollectorAgent
from .extractor import ExtractorAgent
from .evaluator import EvaluatorAgent
from models.constants import TaskStatus

class ControllerAgent(BaseAgent):
    """控制器智能体，负责调度整个系统的任务流程"""
    
    def __init__(
        self,
        name: str = "控制者",
        role: str = "建筑安全分析总协调专家",
        goal: str = "协调和管理整个建筑安全隐患预测流程",
        backstory: Optional[str] = None,
        llm_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False
    ):
        """初始化控制器智能体"""
        backstory = backstory or "建筑安全分析总协调专家，负责协调各个智能体协同工作，确保整个流程顺畅高效。"

        super().__init__(
            name=name,
            role=role,
            goal=goal,
            backstory=backstory,
            llm_config=llm_config,
            verbose=verbose
        )

    def initialize_agents(self) -> Dict[str, BaseAgent]:
        """
        初始化所有智能体
        
        Returns:
            Dict[str, BaseAgent]: 智能体字典
        """
        self.log_info("初始化智能体")
        
        try:
            # 初始化智能体，添加详细日志
            agents = {}
            
            # 初始化收集器智能体
            self.log_info("初始化收集器智能体...")
            agents["collector"] = CollectorAgent()
            
            # 初始化提取器智能体
            self.log_info("初始化提取器智能体...")
            agents["extractor"] = ExtractorAgent()
            
            # 初始化评估器智能体
            self.log_info("初始化评估器智能体...")
            agents["evaluator"] = EvaluatorAgent()

            self.log_info("所有智能体初始化完成")
            return agents
            
        except Exception as e:
            self.log_error("初始化智能体失败", e)
            raise SystemException(f"初始化智能体失败: {str(e)}", code=ERR_SYSTEM)
    
    @safe_execute
    def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行控制器任务
        
        Args:
            task_input: 任务输入，包含以下字段：
                - mode: 运行模式，可选值为 "full", "collect", "extract", "evaluate", "report"
                - params: 任务参数
                - task_id: 任务ID（可选）
                
        Returns:
            Dict[str, Any]: 任务输出
        """
        # 验证输入
        if "mode" not in task_input:
            raise ValueError("缺少必要参数: mode")
        
        mode = task_input.get("mode")
        params = task_input.get("params", {})
        task_id = task_input.get("task_id", data_service.generate_task_id())
        
        self.log_info(f"开始执行任务，模式: {mode}, 任务ID: {task_id}")
        start_time = time.time()
        
        # 初始化智能体
        agents = self.initialize_agents()
        
        # 获取系统配置
        system_config = config_service.get_config("system", as_dict=True)
        
        # 验证运行模式
        if mode not in system_config["run_mode"]["available_modes"]:
            self.log_error(f"无效的运行模式: {mode}")
            return {
                "status": "error",
                "error": f"无效的运行模式: {mode}",
                "task_id": task_id
            }
            
        # 记录任务状态
        task_status = {
            "task_id": task_id,
            "mode": mode,
            "params": params,
            "status": TaskStatus.RUNNING,
            "start_time": datetime.now().isoformat(),
            "steps": [],
            "current_step": None,
            "completed_steps": []
        }
        
        # 保存初始状态 - 只在任务开始时保存一次
        data_service.update_task_status(task_id, "controller", TaskStatus.RUNNING, {"mode": mode})
        
        # 根据运行模式确定工作流程
        workflow = system_config["run_mode"]["available_modes"][mode]["workflow"]
        
        # 执行工作流程
        result = {}
        for step in workflow:
            self.log_info(f"执行步骤: {step}")
            
            # 执行步骤
            step_result = self.run_workflow_step(step, agents, params, task_id)
            
            # 更新本地结果记录
            result[step] = step_result
            
            # 如果步骤失败，终止工作流
            if step_result.get("status") != "success":
                self.log_error(f"步骤 {step} 执行失败: {step_result.get('error', '未知错误')}")
                break
            
            # 如果不是最后一个步骤，准备下一个步骤的输入
            if step != workflow[-1]:
                next_step = workflow[workflow.index(step) + 1]
                params = self._transform_step_data(step, next_step, step_result, params)
        
        # 计算执行时间
        execution_time = time.time() - start_time
        
        # 更新任务最终状态 - 任务结束时更新一次
        final_status = "success" if all(r.get("status") == "success" for r in result.values()) else "error"
        data_service.update_task_status(task_id, "controller",
                                       TaskStatus.COMPLETED if final_status == "success" else TaskStatus.FAILED,
                                       {
                                           "end_time": datetime.now().isoformat(),
                                           "execution_time": execution_time
                                       })
        
        # 返回结果
        final_result = {
            "status": final_status,
            "task_id": task_id,
            "execution_time": execution_time,
            "mode": mode,
            **result
        }
        
        self.log_info(f"任务执行完成，耗时: {execution_time:.2f}秒")
        return final_result
    
    def run_workflow_step(self, step: str, agents: Dict[str, BaseAgent], params: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """
        执行工作流步骤
        
        Args:
            step: 步骤名称
            agents: 智能体字典
            params: 任务参数
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 步骤执行结果
        """
        # 根据步骤名称调用相应的方法
        step_methods = {
            "collect": self.run_collection_workflow,
            "extract": self.run_extraction_workflow,
            "evaluate": self.run_evaluation_workflow,
            "report": self.run_reporting_workflow
        }
        
        if step not in step_methods:
            return {
                "status": "error",
                "error": f"未知的步骤: {step}"
            }
        
        try:
            # 执行步骤
            result = step_methods[step](agents, params, task_id)
            return result
        except Exception as e:
            self.log_error(f"执行步骤 {step} 失败", e)
            return {
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def _transform_step_data(self, current_step: str, next_step: str, step_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换步骤数据，为下一个步骤准备输入
        
        Args:
            current_step: 当前步骤名称
            next_step: 下一个步骤名称
            step_result: 当前步骤执行结果
            params: 当前任务参数
            
        Returns:
            Dict[str, Any]: 转换后的任务参数
        """
        # 复制参数
        new_params = params.copy()
        
        # 根据当前步骤和下一个步骤进行转换
        if current_step == "collect" and next_step == "extract":
            # 将收集步骤的结果传递给提取步骤
            if "data" in step_result:
                new_params["data"] = step_result["data"]
            if "weibo_data_path" in step_result:
                new_params["input_file"] = step_result["weibo_data_path"]
        elif current_step == "extract" and next_step == "evaluate":
            # 将提取步骤的结果传递给评估步骤
            if "data" in step_result:
                new_params["data"] = step_result["data"]
            if "structured_data_path" in step_result:
                new_params["input_file"] = step_result["structured_data_path"]
        elif current_step == "evaluate" and next_step == "report":
            # 将评估步骤的结果传递给报告步骤
            if "data" in step_result:
                new_params["data"] = step_result["data"]
            if "evaluation_data_path" in step_result:
                new_params["input_file"] = step_result["evaluation_data_path"]
        
        return new_params

    def run_collection_workflow(self, agents: Dict[str, BaseAgent], params: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """
        执行数据收集工作流
        
        Args:
            agents: 智能体字典
            params: 任务参数
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 工作流执行结果
        """
        self.log_info("开始执行数据收集工作流")
        
        # 准备收集器输入 - 只传递任务ID，所有配置通过settings.py
        collector_input = {
            "task_id": task_id,
            "data_source": "weibo"
        }


        # 如果提供了输入文件，则直接使用
        if "input_file" in params and params["input_file"]:
            self.log_info(f"使用提供的输入文件: {params['input_file']}")
            try:
                # 加载数据
                data = data_service.load_json(params["input_file"])
                return {
                    "status": "success",
                    "data": data,
                    "weibo_data_path": params["input_file"]
                }
            except Exception as e:
                self.log_error(f"加载输入文件失败: {str(e)}")
                return {
                    "status": "error",
                    "error": f"加载输入文件失败: {str(e)}"
                }
        
        # 执行收集任务
        try:
            collector = agents["collector"]
            result = collector.execute(collector_input)
            
            if result.get("status") != "success":
                return result
            
            # 读取数据文件
            data_file = result.get("data_file")
            if data_file and Path(data_file).exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = []
            
            # 返回结果
            return {
                "status": "success",
                "data": data,
                "weibo_data_path": result.get("data_file"),
                "count": result.get("count", len(data))
            }
        except Exception as e:
            self.log_error("执行数据收集工作流失败", e)
            return {
                "status": "error",
                "error": str(e)
            }

    def run_extraction_workflow(self, agents: Dict[str, BaseAgent], params: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """
        执行信息抽取工作流
        
        Args:
            agents: 智能体字典
            params: 任务参数
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 工作流执行结果
        """
        self.log_info("开始执行信息抽取工作流")
        
        # 准备抽取器输入
        extractor_input = {
            "task_id": task_id
        }
        
        # 如果参数中有数据，直接使用
        if "data" in params and params["data"]:
            extractor_input["data"] = params["data"]
        # 如果参数中有输入文件路径，使用文件路径
        elif "input_file" in params and params["input_file"]:
            extractor_input["input_file"] = params["input_file"]
        else:
            return {
                "status": "error",
                "error": "缺少必要的输入数据或文件路径"
            }
        
        # 执行抽取任务
        try:
            extractor = agents["extractor"]
            result = extractor.execute(extractor_input)
            
            if result.get("status") != "success":
                return result
            
            # 返回结果
            return {
                "status": "success",
                "data": result.get("data", []),
                "structured_data_path": result.get("output_file"),
                "count": len(result.get("data", []))
            }
        except Exception as e:
            self.log_error("执行信息抽取工作流失败", e)
            return {
                "status": "error",
                "error": str(e)
            }

    def run_evaluation_workflow(self, agents: Dict[str, BaseAgent], params: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """
        执行风险评估工作流
        
        Args:
            agents: 智能体字典
            params: 任务参数
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 工作流执行结果
        """
        self.log_info("开始执行风险评估工作流")
        
        # 准备评估器输入
        evaluator_input = {
            "task_id": task_id
        }
        
        # 如果参数中有数据，直接使用
        if "data" in params and params["data"]:
            evaluator_input["data"] = params["data"]
        # 如果参数中有输入文件路径，使用文件路径
        elif "input_file" in params and params["input_file"]:
            evaluator_input["input_file"] = params["input_file"]
        else:
            # 创建一个空的数据文件
            self.log_info("没有输入数据，创建空的结构化数据文件")
            empty_data = []
            structured_data_path = data_service.get_task_path(task_id, "structured")
            with open(structured_data_path, 'w', encoding='utf-8') as f:
                json.dump(empty_data, f)
            evaluator_input["input_file"] = str(structured_data_path)
        
        # 执行评估任务
        try:
            evaluator = agents["evaluator"]
            result = evaluator.execute(evaluator_input)
            
            if result.get("status") != "success":
                return result
            
            # 返回结果
            return {
                "status": "success",
                "data": result.get("data", []),
                "evaluation_data_path": result.get("output_file"),
                "count": len(result.get("data", []))
            }
        except Exception as e:
            self.log_error("执行风险评估工作流失败", e)
            return {
                "status": "error",
                "error": str(e)
            }

    def run_reporting_workflow(self, agents: Dict[str, BaseAgent], params: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """
        执行报告生成工作流 - 在控制器内部生成报告

        Args:
            agents: 智能体字典（未使用，保持接口一致性）
            params: 任务参数
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 工作流执行结果，包含TaskResult格式
        """
        self.log_info("开始执行报告生成工作流")
        
        # 获取风险评估数据
        assessment_data = []
        if "data" in params and params["data"]:
            assessment_data = params["data"]
        elif "input_file" in params and params["input_file"]:
            try:
                with open(params["input_file"], 'r', encoding='utf-8') as f:
                    assessment_data = json.load(f)
            except Exception as e:
                return {
                    "status": "error",
                    "error": f"读取评估数据失败: {str(e)}"
                }
        else:
            # 如果没有数据，使用空列表
            self.log_info("没有输入数据，将生成空报告")
            assessment_data = []
        
        # 生成TaskResult格式的报告
        try:
            # 生成TaskResult格式的报告
            task_result = self._generate_task_result(task_id, assessment_data)

            # 设置报告文件路径
            report_path = params.get("report_path")
            if not report_path:
                # 如果没有指定报告路径，使用默认路径
                reports_dir = config_service.get_path("reports_dir")
                report_path = reports_dir / f"{task_id}.md"
                self.log_info(f"未指定报告路径，使用默认路径: {report_path}")

            # 确保报告目录存在
            report_dir = Path(report_path).parent
            report_dir.mkdir(parents=True, exist_ok=True)

            # 保存报告文件
            try:
                # 只保存Markdown格式的报告
                data_service.save_text(task_result["report_markdown"], report_path)
                self.log_info(f"报告已保存到: {report_path}")
            except Exception as e:
                self.log_error(f"保存报告失败: {str(e)}")
                raise TaskException(f"保存报告失败: {str(e)}", task_id=task_id)

            return {
                "status": "success",
                "data": task_result,
                "report_file": str(report_path),
                "report": task_result["report_markdown"]
            }

        except Exception as e:
            self.log_error("生成报告失败", e)
            return {
                "status": "error",
                "error": str(e)
            }

    def _generate_task_result(self, task_id: str, assessment_data: list) -> Dict[str, Any]:
        """
        生成TaskResult格式的报告

        Args:
            task_id: 任务ID
            assessment_data: 风险评估数据列表

        Returns:
            Dict[str, Any]: TaskResult格式的数据
        """
        # 初始化报告生成进度条
        progress_bar = create_progress_bar(
            total=len(assessment_data) + 3,  # 数据分组 + 统计生成 + Markdown生成 + 完成
            stage_name="报告生成",
            logger=self.logger,
            update_threshold=10  # 每10%显示一次进度
        )

        # 按风险等级分组
        risk_groups = {
            "必须立即处理": [],
            "建议整改优化": [],
            "提醒关注观察": [],
            "信息不完整": []
        }

        # 分组评估数据
        for i, assessment in enumerate(assessment_data):
            risk_level = assessment.get("risk_level", "信息不完整")
            if risk_level in risk_groups:
                risk_groups[risk_level].append(assessment)
            else:
                risk_groups["信息不完整"].append(assessment)

            # 更新进度（每处理一定数量的数据更新一次）
            if i % max(1, len(assessment_data) // 10) == 0:
                progress_bar.update(current=i + 1)

        # 生成汇总统计
        progress_bar.update(current=len(assessment_data) + 1)
        total_count = len(assessment_data)
        summary = {
            "total_hazards": total_count,
            "critical_count": len(risk_groups["必须立即处理"]),
            "medium_count": len(risk_groups["建议整改优化"]),
            "low_count": len(risk_groups["提醒关注观察"]),
            "incomplete_count": len(risk_groups["信息不完整"])
        }

        # 生成markdown报告
        progress_bar.update(current=len(assessment_data) + 2)
        report_markdown = self._generate_report_markdown(task_id, risk_groups, summary)

        # 构建TaskResult
        progress_bar.update(current=len(assessment_data) + 3)
        task_result = {
            "task_id": task_id,
            "timestamp": datetime.now().isoformat(),
            "summary": summary,
            "risk_groups": risk_groups,
            "report_markdown": report_markdown
        }

        # 完成进度条
        progress_bar.finish()

        return task_result

    def _generate_report_markdown(self, task_id: str, risk_groups: Dict[str, list], summary: Dict[str, int]) -> str:
        """
        生成markdown格式的报告

        Args:
            task_id: 任务ID
            risk_groups: 按风险等级分组的数据
            summary: 汇总统计

        Returns:
            str: markdown格式的报告
        """
        markdown = f"# 建筑安全隐患分析报告\n\n"
        markdown += f"**任务ID**: {task_id or 'null'}\n\n"
        markdown += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 汇总统计
        markdown += "## 汇总统计\n\n"
        markdown += f"- **总隐患数量**: {summary.get('total_hazards', 0)}\n"
        markdown += f"- **必须立即处理**: {summary.get('critical_count', 0)}\n"
        markdown += f"- **建议整改优化**: {summary.get('medium_count', 0)}\n"
        markdown += f"- **提醒关注观察**: {summary.get('low_count', 0)}\n"
        markdown += f"- **信息不完整**: {summary.get('incomplete_count', 0)}\n\n"

        # 按风险等级生成详细报告 - 确保所有等级都显示
        risk_levels = ["必须立即处理", "建议整改优化", "提醒关注观察", "信息不完整"]

        for risk_level in risk_levels:
            assessments = risk_groups.get(risk_level, [])
            markdown += f"## {risk_level} ({len(assessments)}项)\n\n"

            if assessments:
                for i, assessment in enumerate(assessments, 1):
                    # 生成统一格式的标题
                    hazard_id = assessment.get('hazard_id', 'null')
                    hazard_description = assessment.get('hazard_description', 'null')
                    markdown += f"### {i}. 风险事件：{hazard_id}（{hazard_description}）\n\n"

                    # 生成标准格式的报告内容，支持多路径显示
                    # 使用辅助函数处理空值
                    def format_value(value):
                        return value if value and str(value).strip() else 'null'

                    # 完整的基本信息
                    markdown += f"**隐患ID**: {hazard_id}\n\n"
                    markdown += f"**风险等级**: {format_value(assessment.get('risk_level'))}\n\n"
                    markdown += f"**时间**: {format_value(assessment.get('timestamp'))}\n\n"
                    markdown += f"**地点**: {format_value(assessment.get('location'))}\n\n"
                    markdown += f"**来源**: {format_value(assessment.get('author'))}\n\n"
                    markdown += f"**描述**: {hazard_description}\n\n"

                    # 违规信息
                    violation = assessment.get("violation", False)
                    markdown += f"**是否违规**: {'是' if violation else '否'}\n\n"

                    # 法规信息
                    markdown += f"**相关法规**: {format_value(assessment.get('law_clause'))}\n\n"
                    markdown += f"**法规说明**: {format_value(assessment.get('law_reason'))}\n\n"

                    # 图谱路径 - 支持多行显示
                    graph_path = assessment.get('graph_path', 'null')
                    if graph_path and graph_path != 'null' and '\n' in graph_path:
                        markdown += f"**图谱路径**:\n{graph_path}\n\n"
                    else:
                        markdown += f"**图谱路径**: {graph_path}\n\n"

                    # 推理链条说明
                    markdown += f"**推理链条说明**: {format_value(assessment.get('evidence_chain'))}\n\n"

                    # 微博地址
                    weibo_url = assessment.get('weibo_url', '')
                    markdown += f"**微博地址**: {weibo_url or 'null'}\n\n"

            else:
                markdown += "暂无此等级的隐患。\n\n"

        return markdown
