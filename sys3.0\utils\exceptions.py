"""
异常模块
提供简化的异常处理机制
"""

import functools
import traceback
import logging
from typing import Any, Dict, Optional, Callable, TypeVar

# 定义类型变量
T = TypeVar('T')
R = TypeVar('R')

# 错误代码常量
ERR_SYSTEM = 1000
ERR_CONFIG = 1100
ERR_DATA = 2000
ERR_NETWORK = 4000
ERR_TASK = 5000

class SystemException(Exception):
    """
    系统异常基类
    使用错误代码和上下文参数区分不同类型的异常
    
    Attributes:
        message: 错误消息
        code: 错误代码
        context: 错误上下文信息
    """
    def __init__(self, message: str, code: int = ERR_SYSTEM, **context):
        """
        初始化系统异常
        
        Args:
            message: 错误消息
            code: 错误代码
            **context: 错误上下文信息
        """
        self.message = message
        self.code = code
        self.context = context
        super().__init__(message)
    
    def __str__(self) -> str:
        """
        返回异常的字符串表示
        
        Returns:
            str: 异常的字符串表示
        """
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            return f"[{self.code}] {self.message} ({context_str})"
        return f"[{self.code}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将异常转换为字典
        
        Returns:
            Dict[str, Any]: 异常的字典表示
        """
        result = {
            "code": self.code,
            "error": self.__class__.__name__,
            "message": self.message
        }
        if self.context:
            result["context"] = self.context
        return result

# 简化的异常类型
class ConfigException(SystemException):
    """配置异常"""
    def __init__(self, message: str, **context):
        super().__init__(message, ERR_CONFIG, **context)

class DataException(SystemException):
    """数据异常"""
    def __init__(self, message: str, **context):
        super().__init__(message, ERR_DATA, **context)

class NetworkException(SystemException):
    """网络异常"""
    def __init__(self, message: str, **context):
        super().__init__(message, ERR_NETWORK, **context)

class TaskException(SystemException):
    """任务异常"""
    def __init__(self, message: str, **context):
        super().__init__(message, ERR_TASK, **context)

# 删除兼容性别名，直接使用新的异常类型



# 异常工厂函数，方便创建特定错误代码的异常
def create_exception(message: str, code: int, **context) -> SystemException:
    """
    创建指定错误代码的异常
    
    Args:
        message: 错误消息
        code: 错误代码
        **context: 错误上下文信息
        
    Returns:
        SystemException: 创建的异常实例
    """
    return SystemException(message, code, **context)

def safe_execute(func: Callable[..., R]) -> Callable[..., R]:
    """
    安全执行装饰器
    捕获异常并转换为SystemException
    
    Args:
        func: 要装饰的函数
        
    Returns:
        Callable[..., R]: 装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> R:
        try:
            return func(*args, **kwargs)
        except SystemException:
            # 已经是SystemException，直接抛出
            raise
        except Exception as e:
            # 转换为SystemException
            tb = traceback.format_exc()
            raise SystemException(
                f"{func.__name__}执行失败: {str(e)}",
                code=ERR_SYSTEM,
                original_error=str(e),
                traceback=tb
            )
    return wrapper

# 异常处理上下文管理器
class ExceptionContext:
    """
    异常处理上下文管理器
    用于在with语句中捕获异常并进行处理
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None,
                 error_handler: Optional[Callable[[Exception], Any]] = None,
                 reraise: bool = True):
        """初始化异常处理上下文"""
        self.logger = logger
        self.error_handler = error_handler
        self.reraise = reraise
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            # 记录异常
            if self.logger:
                if isinstance(exc_val, SystemException):
                    self.logger.error(f"捕获到异常: {str(exc_val)}")
                else:
                    self.logger.error(f"捕获到异常: {exc_type.__name__}: {str(exc_val)}")
                    self.logger.debug(traceback.format_exc())
            
            # 处理异常
            if self.error_handler:
                self.error_handler(exc_val)
            
            # 是否重新抛出
            return not self.reraise
        return False

# 导出的名称
__all__ = [
    'SystemException', 'ConfigException', 'DataException',
    'NetworkException', 'TaskException',
    'create_exception', 'safe_execute', 'ExceptionContext',
    # 错误代码
    'ERR_SYSTEM', 'ERR_CONFIG', 'ERR_DATA', 'ERR_NETWORK', 'ERR_TASK'
]