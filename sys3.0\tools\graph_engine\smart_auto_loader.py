#!/usr/bin/env python3
"""
智能图谱数据自动加载器
支持文件变化检测和增量更新
"""

import json
import sys
import time
import hashlib
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# 添加项目根目录到路径
ROOT_DIR = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ROOT_DIR))

from tools.graph_engine.core.database import Neo4jConnection
from utils.logger import get_logger

logger = get_logger("smart_graph_loader")

class SmartGraphAutoLoader:
    """智能图谱数据自动加载器 - 支持变化检测和自动更新"""
    
    def __init__(self, 
                 uri: str = "bolt://localhost:7687",
                 user: str = "neo4j", 
                 password: str = "neo4j123"):
        """
        初始化智能自动加载器
        
        Args:
            uri: Neo4j数据库URI
            user: 用户名
            password: 密码
        """
        self.uri = uri
        self.user = user
        self.password = password
        self.connection = None
        self.graph_data_file = Path(__file__).parent / "graph_data.json"
        self.metadata_file = Path(__file__).parent / ".graph_metadata.json"
        
    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = Neo4jConnection(
                uri=self.uri,
                user=self.user,
                password=self.password
            )
            self.connection.connect()
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def get_file_hash(self) -> str:
        """计算图谱数据文件的哈希值"""
        try:
            with open(self.graph_data_file, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception as e:
            logger.error(f"❌ 计算文件哈希失败: {e}")
            return ""
    
    def get_file_metadata(self) -> Dict[str, Any]:
        """获取文件元数据"""
        try:
            stat = self.graph_data_file.stat()
            return {
                'size': stat.st_size,
                'mtime': stat.st_mtime,
                'hash': self.get_file_hash()
            }
        except Exception as e:
            logger.error(f"❌ 获取文件元数据失败: {e}")
            return {}
    
    def load_stored_metadata(self) -> Dict[str, Any]:
        """加载存储的元数据"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ 加载元数据失败: {e}")
        return {}
    
    def save_metadata(self, metadata: Dict[str, Any]):
        """保存元数据"""
        try:
            metadata['last_updated'] = datetime.now().isoformat()
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            logger.info("✅ 元数据保存成功")
        except Exception as e:
            logger.error(f"❌ 保存元数据失败: {e}")
    
    def has_file_changed(self) -> bool:
        """检查文件是否有变化"""
        current_metadata = self.get_file_metadata()
        stored_metadata = self.load_stored_metadata()
        
        if not stored_metadata:
            logger.info("📊 首次运行，需要加载数据")
            return True
        
        # 比较哈希值
        current_hash = current_metadata.get('hash', '')
        stored_hash = stored_metadata.get('hash', '')
        
        if current_hash != stored_hash:
            logger.info("📊 检测到文件变化，需要更新数据")
            logger.info(f"   当前哈希: {current_hash}")
            logger.info(f"   存储哈希: {stored_hash}")
            return True
        
        logger.info("📊 文件未变化，无需更新")
        return False
    
    def check_database_status(self) -> Dict[str, Any]:
        """检查数据库状态"""
        try:
            # 检查节点数量
            result = self.connection.execute_query("MATCH (n) RETURN count(n) as count")
            node_count = result[0]['count'] if result else 0
            
            # 检查向量索引
            index_result = self.connection.execute_query("SHOW INDEXES")
            vector_indices = [r for r in index_result if 'VECTOR' in str(r)]
            
            # 检查embedding数据（5个节点类型）
            embedding_counts = {}
            for node_type in ['Case', 'HazardBehavior', 'WarningSignal', 'TriggerCause', 'Damage']:
                embed_result = self.connection.execute_query(f"""
                MATCH (n:{node_type})
                WHERE n.embedding IS NOT NULL
                RETURN count(n) as count
                """)
                embedding_counts[node_type] = embed_result[0]['count'] if embed_result else 0
            
            return {
                'node_count': node_count,
                'vector_indices': len(vector_indices),
                'embedding_counts': embedding_counts,
                'is_empty': node_count == 0
            }
            
        except Exception as e:
            logger.error(f"❌ 检查数据库状态失败: {e}")
            return {'is_empty': True, 'error': str(e)}
    
    def should_update_data(self) -> tuple[bool, str]:
        """判断是否需要更新数据"""
        # 1. 检查数据库状态
        db_status = self.check_database_status()
        
        # 2. 如果数据库为空，必须加载
        if db_status.get('is_empty', True):
            return True, "数据库为空，需要初始加载"
        
        # 3. 检查文件是否有变化
        if self.has_file_changed():
            return True, "检测到文件变化，需要更新数据"
        
        # 4. 检查数据完整性
        embedding_counts = db_status.get('embedding_counts', {})
        total_embeddings = sum(embedding_counts.values())
        
        if total_embeddings == 0:
            return True, "数据库缺少embedding数据，需要重新加载"
        
        if db_status.get('vector_indices', 0) < 5:
            return True, "向量索引不完整，需要重新加载"
        
        return False, "数据库状态正常，无需更新"
    
    def load_graph_data(self) -> Dict[str, Any]:
        """加载图谱数据文件"""
        try:
            with open(self.graph_data_file, 'r', encoding='utf-8') as f:
                graph_data = json.load(f)

            logger.info(f"✅ 读取图谱数据成功: {len(graph_data['nodes'])} 个节点, {len(graph_data['relationships'])} 个关系")
            return graph_data

        except Exception as e:
            logger.error(f"❌ 读取图谱数据失败: {e}")
            return None

    def clear_database(self) -> bool:
        """清空数据库"""
        try:
            # 先检查当前节点数量
            result = self.connection.execute_query("MATCH (n) RETURN count(n) as count")
            before_count = result[0]['count'] if result else 0

            if before_count > 0:
                logger.info(f"🗑️ 清空现有数据 ({before_count} 个节点)...")

                # 删除所有节点和关系
                self.connection.execute_query("MATCH (n) DETACH DELETE n")

                # 验证清空结果
                result = self.connection.execute_query("MATCH (n) RETURN count(n) as count")
                after_count = result[0]['count'] if result else 0

                if after_count == 0:
                    logger.info("✅ 数据库已完全清空")
                else:
                    logger.warning(f"⚠️ 数据库清空不完整，仍有 {after_count} 个节点")
                    return False
            else:
                logger.info("📊 数据库已为空，无需清空")

            return True
        except Exception as e:
            logger.error(f"❌ 清空数据库失败: {e}")
            return False

    def create_nodes(self, nodes: List[Dict[str, Any]]) -> int:
        """创建节点"""
        logger.info("📝 开始创建节点...")
        node_count = 0

        for node in nodes:
            try:
                # 根据实际数据结构构建查询
                # 数据文件中节点结构: id, label, type, 以及其他直接字段
                node_type = node.get('type', 'Unknown')

                # 构建基础查询 - 使用标题化的类型作为标签
                query = f"CREATE (n:{node_type.title().replace('_', '')})"

                # 设置基础属性
                params = {
                    'id': node.get('id', ''),
                    'label': node.get('label', ''),
                    'type': node_type
                }

                # 构建SET子句
                set_clauses = [
                    "n.id = $id",
                    "n.label = $label",
                    "n.type = $type"
                ]

                # 添加其他字段（根据实际数据结构）
                for key, value in node.items():
                    if key not in ['id', 'label', 'type']:
                        param_key = f"param_{key}"
                        set_clauses.append(f"n.{key} = ${param_key}")
                        params[param_key] = value

                # 完整查询
                query += " SET " + ", ".join(set_clauses)

                self.connection.execute_query(query, params)
                node_count += 1

                if node_count % 10 == 0:
                    logger.info(f"   已创建 {node_count} 个节点...")

            except Exception as e:
                logger.error(f"❌ 创建节点失败 {node.get('id', 'unknown')}: {e}")

        logger.info(f"✅ 创建节点完成: {node_count} 个")
        return node_count

    def create_relationships(self, relationships: List[Dict[str, Any]]) -> int:
        """创建关系"""
        logger.info("🔗 开始创建关系...")
        rel_count = 0

        for rel in relationships:
            try:
                # 根据实际数据结构构建关系查询
                # 数据文件中关系结构: source, target, relation, 以及其他直接字段
                relation_type = rel.get('relation', 'RELATES_TO').upper()

                query = f"""
                MATCH (a {{id: $source_id}}), (b {{id: $target_id}})
                CREATE (a)-[r:{relation_type}]->(b)
                SET r.relation = $relation
                """

                # 基础参数
                params = {
                    'source_id': rel.get('source', ''),
                    'target_id': rel.get('target', ''),
                    'relation': rel.get('relation', '')
                }

                # 添加其他字段
                for key, value in rel.items():
                    if key not in ['source', 'target', 'relation']:
                        param_key = f"param_{key}"
                        query += f", r.{key} = ${param_key}"
                        params[param_key] = value

                self.connection.execute_query(query, params)
                rel_count += 1

                if rel_count % 10 == 0:
                    logger.info(f"   已创建 {rel_count} 个关系...")

            except Exception as e:
                logger.error(f"❌ 创建关系失败 {rel.get('source', 'unknown')}->{rel.get('target', 'unknown')}: {e}")

        logger.info(f"✅ 创建关系完成: {rel_count} 个")
        return rel_count

    def verify_data(self) -> bool:
        """验证数据"""
        logger.info("🔍 验证数据...")
        try:
            # 统计节点
            result = self.connection.execute_query("MATCH (n) RETURN labels(n)[0] as label, count(n) as count")
            total_nodes = 0
            for record in result:
                logger.info(f"   {record['label']}: {record['count']} 个节点")
                total_nodes += record['count']

            # 统计关系
            result = self.connection.execute_query("MATCH ()-[r]->() RETURN count(r) as count")
            rel_total = result[0]['count']
            logger.info(f"   关系总数: {rel_total}")

            logger.info(f"✅ 数据验证完成: 总计 {total_nodes} 个节点, {rel_total} 个关系")
            return True

        except Exception as e:
            logger.error(f"❌ 验证数据失败: {e}")
            return False

    def load_and_update_data(self, force_reload: bool = False) -> bool:
        """加载和更新数据"""
        try:
            # 1. 读取图谱数据
            graph_data = self.load_graph_data()
            if not graph_data:
                return False

            # 2. 清空现有数据（避免重复数据）
            if not self.clear_database():
                logger.error("❌ 清空数据库失败，停止加载")
                return False

            # 3. 创建节点
            node_count = self.create_nodes(graph_data['nodes'])
            if node_count == 0:
                logger.error("❌ 未能创建任何节点")
                return False

            # 4. 创建关系
            rel_count = self.create_relationships(graph_data['relationships'])

            # 5. 验证数据
            if not self.verify_data():
                return False

            # 6. 初始化向量索引和embedding
            self._initialize_vector_system()

            # 7. 更新元数据
            current_metadata = self.get_file_metadata()
            self.save_metadata(current_metadata)
            logger.info("✅ 数据加载和元数据更新完成")

            return True

        except Exception as e:
            logger.error(f"❌ 数据加载失败: {e}")
            return False

    def _initialize_vector_system(self):
        """初始化向量系统"""
        try:
            logger.info("🔧 初始化向量索引和embedding...")

            # 导入向量初始化工具
            from tools.graph_engine.init_vector_indices import main as init_vectors

            # 执行向量初始化
            init_vectors()
            logger.info("✅ 向量系统初始化完成")

        except Exception as e:
            logger.warning(f"⚠️ 向量系统初始化失败: {e}")
            # 不抛出异常，允许基础数据加载完成
    
    def smart_auto_load(self, force_reload: bool = False) -> bool:
        """
        智能自动加载
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            bool: 加载是否成功
        """
        logger.info("🚀 开始智能图谱数据检查和加载")
        
        # 1. 连接数据库
        if not self.connect_database():
            return False
        
        # 2. 判断是否需要更新
        if force_reload:
            need_update = True
            reason = "强制重新加载"
        else:
            need_update, reason = self.should_update_data()
        
        logger.info(f"📋 更新检查结果: {reason}")
        
        # 3. 执行更新
        if need_update:
            logger.info("🔄 开始更新图谱数据...")
            success = self.load_and_update_data(force_reload=force_reload)
            
            if success:
                logger.info("🎉 图谱数据更新完成！")
            else:
                logger.error("❌ 图谱数据更新失败！")
            
            return success
        else:
            logger.info("✅ 图谱数据已是最新，无需更新")
            return True
    
    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()

def smart_auto_load_graph_data(force_reload: bool = False) -> bool:
    """
    便捷函数：智能自动加载图谱数据
    
    Args:
        force_reload: 是否强制重新加载
        
    Returns:
        bool: 加载是否成功
    """
    loader = SmartGraphAutoLoader()
    try:
        return loader.smart_auto_load(force_reload)
    finally:
        loader.close()

if __name__ == "__main__":
    # 支持命令行参数
    import argparse
    
    parser = argparse.ArgumentParser(description="智能图谱数据自动加载器")
    parser.add_argument("--force", action="store_true", help="强制重新加载数据")
    parser.add_argument("--check", action="store_true", help="只检查状态，不更新")
    args = parser.parse_args()
    
    if args.check:
        loader = SmartGraphAutoLoader()
        if loader.connect_database():
            need_update, reason = loader.should_update_data()
            print(f"更新检查: {reason}")
            print(f"需要更新: {'是' if need_update else '否'}")
        loader.close()
    else:
        success = smart_auto_load_graph_data(force_reload=args.force)
        sys.exit(0 if success else 1)
