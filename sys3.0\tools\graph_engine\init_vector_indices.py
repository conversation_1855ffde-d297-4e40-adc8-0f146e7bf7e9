"""
图谱向量索引初始化工具
为4个节点类型创建向量索引并生成embedding
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ROOT_DIR))

from tools.graph_engine.core.database import Neo4jConnection
from utils.logger import get_logger

logger = get_logger(__name__)


def main():
    """主函数：初始化向量索引和embedding"""
    connection = None
    try:
        logger.info("🚀 开始初始化图谱向量系统...")

        # 直接使用配置（与SmartGraphAutoLoader保持一致）
        # 连接数据库
        connection = Neo4jConnection(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="neo4j123",
            database="neo4j"
        )
        
        if not connection.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        logger.info("✅ 数据库连接成功")
        
        # 1. 创建向量索引
        logger.info("📝 创建向量索引...")
        if connection.create_all_vector_indices():
            logger.info("✅ 向量索引创建完成")
        else:
            logger.warning("⚠️ 部分向量索引创建失败")
        
        # 2. 为现有节点生成向量
        print("🔧 为现有节点生成向量...")
        if connection.vectorize_existing_nodes():
            pass  # 向量化完成信息已在内部输出
        else:
            logger.warning("⚠️ 节点向量化失败")
        
        # 3. 验证向量系统
        print("🔍 验证向量系统...")

        # 检查向量索引
        index_result = connection.execute_query("SHOW INDEXES")
        vector_indices = [r for r in index_result if 'VECTOR' in str(r)]

        # 检查embedding数据
        node_types = ['Case', 'HazardBehavior', 'WarningSignal', 'TriggerCause', 'Damage']
        total_embeddings = 0

        for node_type in node_types:
            embed_result = connection.execute_query(f"""
            MATCH (n:{node_type})
            WHERE n.embedding IS NOT NULL
            RETURN count(n) as count
            """)
            count = embed_result[0]['count'] if embed_result else 0
            total_embeddings += count

        print(f"📊 向量索引: {len(vector_indices)} 个，总向量: {total_embeddings} 个")

        if len(vector_indices) >= 5 and total_embeddings > 0:
            print("🎉 向量系统初始化成功！")
            return True
        else:
            print("⚠️ 向量系统初始化不完整")
            return False
            
    except Exception as e:
        logger.error(f"❌ 向量系统初始化失败: {e}")
        return False
        
    finally:
        if connection:
            connection.close()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
