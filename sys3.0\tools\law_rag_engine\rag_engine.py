"""
法规检索引擎
基于向量数据库和重排序模型实现的法规文档检索系统
"""

import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from langchain_community.document_loaders import DirectoryLoader, TextLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain.schema import Document
from langchain_community.vectorstores import FAISS
from langchain_huggingface import HuggingFaceEmbeddings
from sentence_transformers import CrossEncoder

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent  # 指向sys3.0目录
sys.path.insert(0, str(ROOT_DIR))

from config.config import config_service
from utils.logger import get_logger, LoggerMixin

# 定义本地工具接口，避免循环导入
class ToolBase:
    """工具接口基类，所有工具模块都应实现这个接口"""
    
    def __init__(self, config=None):
        """初始化工具
        
        Args:
            config: 工具配置
        """
        self.config = config or {}
    
    def execute(self, input_data):
        """执行工具功能
        
        Args:
            input_data: 输入数据
            
        Returns:
            执行结果
        """
        raise NotImplementedError("子类必须实现execute方法")

class LawRAGEngine(LoggerMixin, ToolBase):
    """法规检索引擎类"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """获取法规引擎单例实例"""
        if cls._instance is None:
            cls._instance = LawRAGEngine()
        return cls._instance
    
    def __init__(self, config=None):
        """初始化法规检索引擎"""
        LoggerMixin.__init__(self)
        ToolBase.__init__(self, config)
        
        self.log_info("初始化法规检索引擎")
        
        # 获取法规配置
        if config:
            self.law_config = config
        else:
            self.law_config = config_service.get_law_config()

        # 使用本地模型路径
        model_name = self.law_config.get("embedding_model")
        local_model_path = ROOT_DIR / "models" / model_name

        # 获取向量存储路径
        vector_store_path = self.law_config.get("vector_store_path")
        if not vector_store_path:
            # 如果配置中没有，则使用默认路径
            vector_store_path = str(ROOT_DIR / "tools" / "law_rag_engine" / "vector")
        
        # 确保路径是Path对象
        self.vector_store_path = Path(vector_store_path)
        
        # 初始化嵌入模型，使用本地模型路径，启用GPU加速
        import torch
        device = "cuda" if torch.cuda.is_available() else "cpu"
        self.log_info(f"法规RAG引擎使用设备: {device}")

        self.embeddings = HuggingFaceEmbeddings(
            model_name=str(local_model_path),
            cache_folder=str(self.vector_store_path.parent / "cache"),
            model_kwargs={"device": device},
            encode_kwargs={"normalize_embeddings": True, "device": device}
        )

        # 初始化重排序模型
        reranker_model_name = self.law_config.get("reranker_model", "bge-reranker-base")
        reranker_model_path = ROOT_DIR / "models" / reranker_model_name

        self.log_info(f"初始化重排序模型: {reranker_model_path}")
        try:
            if reranker_model_path.exists():
                # 显式配置GPU设备和优化参数
                self.reranker = CrossEncoder(
                    str(reranker_model_path),
                    device=device,
                    max_length=512
                )
                self.log_info(f"重排序模型初始化成功，使用设备: {device}")

                # 模型预热 - 执行一次小的预测来加载模型到内存
                try:
                    self.reranker.predict([("测试", "测试文档")])
                    self.log_info("重排序模型预热完成")
                except:
                    pass  # 预热失败不影响主要功能

            else:
                self.log_warning(f"本地重排序模型不存在: {reranker_model_path}")
                # 尝试使用在线模型作为备用
                self.reranker = CrossEncoder(
                    "BAAI/bge-reranker-base",
                    device=device,
                    max_length=512
                )
                self.log_info(f"使用在线重排序模型，设备: {device}")
        except Exception as e:
            self.log_error(f"重排序模型初始化失败: {str(e)}", e)
            self.reranker = None
        
        # 检查向量存储文件是否存在
        index_file = self.vector_store_path / "index.faiss"
        if self.vector_store_path.exists() and index_file.exists():
            self.log_info(f"从 {self.vector_store_path} 加载向量存储")
            try:
                self.vectorstore = FAISS.load_local(
                    str(self.vector_store_path),
                    self.embeddings,
                    allow_dangerous_deserialization=True  # 添加参数，允许反序列化
                )
                # 保存路径到向量存储实例，用于资源清理
                self.vectorstore._path = str(self.vector_store_path)
            except Exception as e:
                self.log_error(f"加载向量存储失败: {str(e)}", e)
                self.vectorstore = None
        else:
            self.log_info("向量存储文件不存在，将在首次使用时创建")
            self.vectorstore = None
        
        # 初始化检索器
        if self.vectorstore:
            self.retriever = self.vectorstore.as_retriever(
                search_kwargs={"k": self.law_config.get("top_k", 5)}
            )
        else:
            self.retriever = None
            
        # 向量存储已初始化
        self.log_info("法规向量存储初始化完成")
    
    def execute(self, input_data):
        """执行法律检索
        
        Args:
            input_data: 包含查询文本的字典，格式如下:
                {
                    'query': '查询文本',
                    'top_k': 5  # 可选，默认为5
                }
            
        Returns:
            str: 检索结果
        """
        query = input_data.get('query')
        top_k = input_data.get('top_k', 5)
        
        if not query:
            return "错误：缺少查询文本"
        
        return self.search_laws(query, top_k)
    
    def _ensure_vectorstore(self):
        """
        确保向量存储已初始化
        
        该方法检查向量存储是否已初始化，如果未初始化，则加载法规文档，
        创建向量存储并保存到磁盘。
        
        Returns:
            bool: 向量存储是否可用
        """
        if self.vectorstore is None:
            self.log_info("初始化向量存储")
            # 加载法规文档
            laws_dir = self.law_config.get("laws_dir")
            if not laws_dir:
                # 如果配置中没有，则使用默认路径
                laws_dir = str(ROOT_DIR / "tools" / "law_rag_engine" / "laws")
            
            # 确保路径是Path对象
            laws_dir = Path(laws_dir)
            
            loader = DirectoryLoader(
                str(laws_dir),
                glob="**/*.md",
                loader_cls=TextLoader,
                loader_kwargs={"encoding": "utf-8"}
            )
            documents = loader.load()
            
            if not documents:
                self.log_warning(f"未在 {laws_dir} 找到法规文档")
                return False
            
            self.log_info(f"加载了 {len(documents)} 个法规文档")
            
            # 分割文档
            text_splitter = CharacterTextSplitter(
                chunk_size=self.law_config.get("chunk_size", 1000),
                chunk_overlap=self.law_config.get("chunk_overlap", 200),
                separator="\n"
            )
            
            chunks = text_splitter.split_documents(documents)
            self.log_info(f"将文档分割为 {len(chunks)} 个块")
            
            # 创建向量存储
            self.vectorstore = FAISS.from_documents(chunks, self.embeddings)
            
            # 初始化检索器
            self.retriever = self.vectorstore.as_retriever(
                search_kwargs={"k": self.law_config.get("top_k", 5)}
            )
            
            # 保存向量存储
            self.vector_store_path.mkdir(parents=True, exist_ok=True)
            self.vectorstore.save_local(str(self.vector_store_path))
            # 保存路径到向量存储实例，用于资源清理
            self.vectorstore._path = str(self.vector_store_path)
            self.log_info(f"向量存储已保存到 {self.vector_store_path}")

            return True
        
        return True
    
    def _search_internal(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        内部搜索方法 - 使用重排序模型

        Args:
            query: 查询文本
            top_k: 返回的最大结果数量

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        if not self._ensure_vectorstore():
            return []

        try:
            # 第一阶段：向量检索，召回更多候选（进一步优化）
            retrieve_k = min(max(top_k * 2, 10), 20)  # 召回10-20个候选，优先性能
            candidates = self.vectorstore.similarity_search(query, k=retrieve_k)

            if not candidates:
                return []

            # 第二阶段：重排序
            if self.reranker is None:
                self.log_warning("重排序模型未初始化，使用原始向量检索结果")
                # 降级到原始方法
                docs_with_scores = self.vectorstore.similarity_search_with_score(query, k=top_k)
                results = []
                similarity_threshold = self.law_config.get("similarity_threshold", 0.7)

                for doc, score in docs_with_scores:
                    similarity_score = max(0.0, 1.0 - score / 2.0)
                    # 过滤相似度过低的结果
                    if similarity_score >= similarity_threshold:
                        results.append({
                            "content": doc.page_content,
                            "metadata": doc.metadata.copy() if hasattr(doc, "metadata") else {},
                            "score": similarity_score
                        })
                    else:
                        self.log_info(f"过滤低相似度法规条文 (相似度: {similarity_score:.3f})")

                return results

            # 构建查询-文档对
            query_doc_pairs = [(query, doc.page_content) for doc in candidates]

            # 重排序打分 - 使用批处理提高性能
            batch_size = 8  # 进一步减小批处理大小以提高性能
            rerank_scores = []

            for i in range(0, len(query_doc_pairs), batch_size):
                batch_pairs = query_doc_pairs[i:i+batch_size]
                batch_scores = self.reranker.predict(batch_pairs)
                rerank_scores.extend(batch_scores)

            # 组合结果并排序
            results = []
            rerank_threshold = self.law_config.get("rerank_threshold", 0.8)

            filtered_count = 0
            for doc, rerank_score in zip(candidates, rerank_scores):
                if rerank_score >= rerank_threshold:
                    metadata = doc.metadata.copy() if hasattr(doc, "metadata") else {}
                    metadata["rerank_score"] = float(rerank_score)

                    results.append({
                        "content": doc.page_content,
                        "metadata": metadata,
                        "score": float(rerank_score)
                    })
                else:
                    filtered_count += 1

            if filtered_count > 0:
                self.log_info(f"过滤了 {filtered_count} 条低相似度法规条文 (阈值: {rerank_threshold})")

            # 按重排序分数排序，返回top_k
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:top_k]

        except Exception as e:
            self.log_error(f"搜索法规时出错: {str(e)}", e)
            return []
    def search_laws(self, query: str, top_k: int = 5) -> str:
        """
        搜索相关法规
        
        Args:
            query: 查询文本
            top_k: 返回的最大结果数量
            
        Returns:
            str: 搜索结果，格式化为Markdown文本
        """
        results = self._search_internal(query, top_k)
        
        if not results:
            return "未找到相关法规"
        
        # 格式化结果为Markdown
        markdown = f"## 关于\"{query}\"的法规检索结果\n\n"
        
        for i, result in enumerate(results):
            markdown += f"### 结果 {i+1}\n\n"
            markdown += result["content"] + "\n\n"
            
            # 添加元数据（如果有）
            if result["metadata"]:
                markdown += "**来源信息**\n\n"
                for key, value in result["metadata"].items():
                    if key != "score":  # 不显示分数
                        markdown += f"- {key}: {value}\n"
                markdown += "\n"
        
        return markdown
    
    def batch_search_laws(self, query_list: List[str], top_k: int = 3) -> Dict[str, str]:
        """
        批量搜索相关法规
        
        Args:
            query_list: 查询文本列表
            top_k: 每个查询返回的最大结果数量
            
        Returns:
            Dict[str, str]: 搜索结果字典，键为查询文本，值为搜索结果
        """
        results = {}
        
        for query in query_list:
            results[query] = self.search_laws(query, top_k)
            
        return results
    
    def batch_search_laws_text(self, queries: str, top_k: int = 3) -> str:
        """
        批量搜索相关法规（文本输入）
        
        Args:
            queries: 查询文本，每行一个查询
            top_k: 每个查询返回的最大结果数量
            
        Returns:
            str: 搜索结果，格式化为Markdown文本
        """
        query_list = [q.strip() for q in queries.split('\n') if q.strip()]
        results = self.batch_search_laws(query_list, top_k)
        
        markdown = "# 批量法规检索结果\n\n"
        
        for query, result in results.items():
            markdown += f"## 查询: {query}\n\n"
            markdown += result + "\n\n---\n\n"
            
        return markdown
    
    def add_document(self, document_name: str, content: str) -> bool:
        """
        添加文档到向量数据库
        
        Args:
            document_name: 文档名称
            content: 文档内容
            
        Returns:
            bool: 是否成功添加
        """
        try:
            # 确保向量存储已初始化
            if not self._ensure_vectorstore():
                return False
            
            # 创建文档对象
            doc = Document(
                page_content=content,
                metadata={"source": document_name}
            )
            
            # 分割文档
            text_splitter = CharacterTextSplitter(
                chunk_size=self.law_config.get("chunk_size", 1000),
                chunk_overlap=self.law_config.get("chunk_overlap", 200),
                separator="\n"
            )
            
            chunks = text_splitter.split_documents([doc])
            
            # 添加到向量存储
            self.vectorstore.add_documents(chunks)
            
            # 保存向量存储
            self.vectorstore.save_local(str(self.vector_store_path))
            
            self.log_info(f"已添加文档 {document_name} 到向量存储")
            return True
            
        except Exception as e:
            self.log_error(f"添加文档失败: {str(e)}", e)
            return False
    
    def add_law_document(self, content: str, document_name: str) -> str:
        """
        添加法规文档到向量数据库
        
        Args:
            content: 文档内容
            document_name: 文档名称
            
        Returns:
            str: 添加结果
        """
        if not document_name:
            document_name = f"law_{int(time.time())}"
            
        success = self.add_document(document_name, content)
        
        if success:
            return f"成功添加法规文档 {document_name}"
        else:
            return f"添加法规文档 {document_name} 失败"