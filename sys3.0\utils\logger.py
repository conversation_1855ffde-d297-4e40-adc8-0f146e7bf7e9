"""
日志模块
提供简化的日志记录功能
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Optional, Dict, Any, Union
import functools
import time

# 默认日志格式
DEFAULT_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# 日志级别映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# 日志实例缓存
_logger_instances = {}

def get_logger(name: str = "system", level: str = "INFO",
              log_file: Optional[Union[str, Path]] = None,
              console: bool = True) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径，如果指定则该logger独立输出到文件
        console: 是否输出到控制台

    Returns:
        logging.Logger: 日志记录器
    """
    # 如果已经存在，直接返回
    if name in _logger_instances:
        return _logger_instances[name]

    # 创建新的日志记录器
    logger = logging.getLogger(name)

    # 设置日志级别
    log_level = LOG_LEVELS.get(level.upper(), logging.INFO)
    logger.setLevel(log_level)

    # 移除所有现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter(DEFAULT_FORMAT, DEFAULT_DATE_FORMAT)

    # 只有明确指定log_file时才添加文件处理器
    # 否则依赖根logger的文件处理器（通过传播）
    if log_file:
        log_path = Path(log_file)

        # 确保日志目录存在
        log_dir = log_path.parent
        os.makedirs(log_dir, exist_ok=True)

        file_handler = RotatingFileHandler(
            filename=str(log_path),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8"
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 如果有独立的文件处理器，添加控制台处理器并禁止传播避免重复
        if console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        logger.propagate = False
    else:
        # 没有独立文件处理器时，不添加控制台处理器，允许传播到根logger进行统一输出
        # 这样避免了重复的控制台输出
        logger.propagate = True

    # 存储实例
    _logger_instances[name] = logger
    return logger

# 日志混入类，可以被其他类继承
class LoggerMixin:
    """
    日志混入类
    提供日志记录方法
    """
    
    @property
    def _logger_name(self) -> str:
        """获取日志记录器名称"""
        return getattr(self, "__logger_name", self.__class__.__name__)
        
    @_logger_name.setter
    def _logger_name(self, name: str):
        """设置日志记录器名称"""
        self.__logger_name = name
        
    @property
    def logger(self) -> logging.Logger:
        """获取日志记录器"""
        return get_logger(self._logger_name)
        
    def set_logger_name(self, name: str):
        """设置日志记录器名称"""
        self._logger_name = name
        
    def log_debug(self, message: str, exc_info: Optional[Exception] = None):
        """记录调试级别日志"""
        self.logger.debug(message, exc_info=exc_info)
        
    def log_info(self, message: str):
        """记录信息级别日志"""
        self.logger.info(message)
        
    def log_warning(self, message: str):
        """记录警告级别日志"""
        self.logger.warning(message)
        
    def log_error(self, message: str, exc_info: Optional[Exception] = None):
        """记录错误级别日志"""
        self.logger.error(message, exc_info=exc_info)
        
    def log_critical(self, message: str, exc_info: Optional[Exception] = None):
        """记录严重错误级别日志"""
        self.logger.critical(message, exc_info=exc_info)

def setup_logging(config: Optional[Dict[str, Any]] = None):
    """
    配置日志系统
    
    Args:
        config: 日志配置，包含以下字段:
            - level: 日志级别
            - file: 日志文件路径
            - console: 是否输出到控制台
            - format: 日志格式
            - date_format: 日期格式
    """
    if config is None:
        config = {}
        
    # 获取配置
    level = config.get('level', os.environ.get('LOG_LEVEL', 'INFO'))
    log_file = config.get('file')
    console = config.get('console', True)
    log_format = config.get('format', DEFAULT_FORMAT)
    date_format = config.get('date_format', DEFAULT_DATE_FORMAT)
    
    # 设置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(LOG_LEVELS.get(level.upper(), logging.INFO))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        
    # 创建格式化器
    formatter = logging.Formatter(log_format, date_format)
    
    # 添加控制台处理器
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 添加文件处理器
    if log_file:
        log_path = Path(log_file)
        
        try:
            # 确保日志目录存在
            log_dir = log_path.parent
            os.makedirs(log_dir, exist_ok=True)
            
            file_handler = RotatingFileHandler(
                filename=str(log_path),
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding="utf-8"
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            # 如果创建日志文件失败，记录错误并继续使用控制台输出
            print(f"警告: 无法创建日志文件 {log_file}: {str(e)}", file=sys.stderr)
            
    # 根logger不禁用传播，它负责接收其他logger传播的日志
    # 并统一输出到文件，避免多个logger重复写入同一文件

def log_execution_time(func):
    """记录函数执行时间的装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger = get_logger()
        logger.debug(f"函数 {func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        
        return result
    return wrapper

# 导出的名称
__all__ = [
    'LoggerMixin', 'setup_logging', 'get_logger', 'log_execution_time'
]
