"""
中间件模块
定义微博爬虫的各种中间件，用于处理请求和响应
"""

import logging
import random
import time
from scrapy import signals
from scrapy.exceptions import IgnoreRequest


class WeiboSpiderMiddleware:
    """微博爬虫中间件
    
    处理爬虫与引擎之间的通信
    """
    @classmethod
    def from_crawler(cls, crawler):
        """创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            WeiboSpiderMiddleware: 中间件实例
        """
        middleware = cls()
        crawler.signals.connect(middleware.spider_opened, signal=signals.spider_opened)
        return middleware
    
    def process_spider_input(self, response, spider):
        """处理爬虫输入
        
        Args:
            response: 响应对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续处理响应
        """
        return None
    
    def process_spider_output(self, response, result, spider):
        """处理爬虫输出
        
        Args:
            response: 响应对象
            result: 生成器，包含Request或Item对象
            spider: 爬虫实例
            
        Returns:
            list: 处理后的结果
        """
        for item in result:
            yield item
    
    def process_spider_exception(self, response, exception, spider):
        """处理爬虫异常
        
        Args:
            response: 响应对象
            exception: 异常对象
            spider: 爬虫实例
            
        Returns:
            list: 处理后的结果，或None表示继续抛出异常
        """
        # 记录异常
        spider.logger.error(f"爬虫异常: {exception}")
        return None
    
    def process_start_requests(self, start_requests, spider):
        """处理起始请求
        
        Args:
            start_requests: 起始请求生成器
            spider: 爬虫实例
            
        Returns:
            list: 处理后的起始请求
        """
        for request in start_requests:
            yield request
    
    def spider_opened(self, spider):
        """爬虫开启时调用
        
        Args:
            spider: 爬虫实例
        """
        spider.logger.info('Spider opened: %s' % spider.name)


class WeiboDownloaderMiddleware:
    """微博下载中间件
    
    处理微博请求的特殊逻辑，确保每个请求都有正确的Cookie
    """
    
    def __init__(self, settings):
        """初始化中间件
        
        Args:
            settings: 爬虫设置
        """
        self.logger = logging.getLogger(__name__)
        self.settings = settings
        self.cookie = settings.get('DEFAULT_REQUEST_HEADERS', {}).get('cookie', '')
        if self.cookie:
            self.logger.info(f"中间件初始化，使用Cookie，长度: {len(self.cookie)}")
        else:
            self.logger.warning("中间件初始化，未找到Cookie配置")
    
    @classmethod
    def from_crawler(cls, crawler):
        """从爬虫创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            WeiboDownloaderMiddleware: 中间件实例
        """
        return cls(crawler.settings)
    
    def process_request(self, request, spider):
        """处理请求
        
        为每个请求添加必要的头信息，确保Cookie正确设置
        
        Args:
            request: 请求对象
            spider: 爬虫实例
            
        Returns:
            None: 继续处理请求
        """
        # 确保每个请求都有User-Agent
        if 'User-Agent' not in request.headers:
            request.headers['User-Agent'] = self.settings.get('DEFAULT_REQUEST_HEADERS', {}).get('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        # 设置cookie
        if getattr(spider, 'cookie', None):
            request.headers['cookie'] = spider.cookie
            spider.logger.debug(f"使用爬虫实例的Cookie")
        elif self.cookie:
            request.headers['cookie'] = self.cookie
            spider.logger.debug(f"为请求 {request.url} 添加Cookie")
        
        # 添加其他必要的头信息
        request.headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        request.headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7'
        
        return None
    
    def process_response(self, request, response, spider):
        """处理响应
        
        检查响应状态，处理重定向和错误情况
        
        Args:
            request: 请求对象
            response: 响应对象
            spider: 爬虫实例
            
        Returns:
            Response: 处理后的响应
        """
        # 检查是否被重定向到登录页面
        if response.status == 302:
            location = response.headers.get('Location', b'').decode('utf-8', errors='ignore')
            if 'passport.weibo.com/visitor/visitor' in location or 'login' in location or 'passport' in location:
                spider.logger.warning(f"请求被重定向到登录页面，可能是Cookie已失效: {request.url} -> {location}")
                if hasattr(spider, 'save_status'):
                    spider.save_status({
                        "warning": "Cookie可能已失效，请更新Cookie",
                        "request": request.url,
                        "redirect": location
                    })
        
        # 检查响应内容是否包含登录提示
        if response.status == 200:
            body_text = response.text
            if "您的登录状态已失效" in body_text or "请登录后再查看" in body_text:
                spider.logger.warning(f"登录状态已失效: {request.url}")
                if hasattr(spider, 'save_status'):
                    spider.save_status({
                        "warning": "登录状态已失效，请更新Cookie",
                        "request": request.url
                    })
        
        return response
    
    def process_exception(self, request, exception, spider):
        """处理下载异常
        
        Args:
            request: 请求对象
            exception: 异常对象
            spider: 爬虫实例
            
        Returns:
            Response或Request或None: 返回None表示继续抛出异常
        """
        # 记录异常信息
        request_id = request.meta.get('request_id', 'unknown')
        spider.logger.error(f"下载异常: {exception.__class__.__name__}: {str(exception)} - {request.url} [ID: {request_id}]")
        
        # 根据异常类型处理
        import twisted.internet.error as twisted_errors
        
        # 连接错误，可能是网络问题
        if isinstance(exception, (twisted_errors.ConnectError, twisted_errors.DNSLookupError)):
            spider.logger.warning(f"连接错误: {str(exception)} [ID: {request_id}]")
            # 记录到爬虫状态
            spider.save_status({"error": f"连接错误: {str(exception)}"})
        
        # 超时错误
        elif isinstance(exception, twisted_errors.TimeoutError):
            spider.logger.warning(f"请求超时: {str(exception)} [ID: {request_id}]")
            # 记录到爬虫状态
            spider.save_status({"error": "请求超时，可能是网络问题或微博服务器繁忙"})
        
        # 尝试重试请求
        retries = request.meta.get('retry_times', 0)
        max_retries = spider.settings.getint('RETRY_TIMES', 3)
        if retries < max_retries:
            spider.logger.info(f"尝试重试请求 [ID: {request_id}], 当前重试次数: {retries+1}/{max_retries}")
            request.meta['retry_times'] = retries + 1
            request.meta['retry_reason'] = f"{exception.__class__.__name__}: {str(exception)}"
            request.dont_filter = True
            
            # 增加延迟，避免频繁请求
            import time
            delay = (retries + 1) * 2  # 递增延迟
            spider.logger.info(f"延迟 {delay} 秒后重试")
            time.sleep(delay)
            
            return request
        else:
            spider.logger.error(f"请求 [ID: {request_id}] 已达到最大重试次数: {max_retries}，放弃重试")
            # 记录到爬虫状态
            spider.save_status({"error": f"请求失败，已达到最大重试次数: {max_retries}"})
        
        return None
    
    def spider_opened(self, spider):
        """爬虫开启时调用
        
        Args:
            spider: 爬虫实例
        """
        spider.logger.info('Spider opened: %s' % spider.name)


class RandomDelayMiddleware:
    """随机延迟中间件
    
    为请求添加随机延迟，减少被检测到的风险
    """
    def __init__(self, delay):
        """初始化延迟中间件
        
        Args:
            delay: 基础延迟时间
        """
        self.delay = delay
    
    @classmethod
    def from_crawler(cls, crawler):
        """创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            RandomDelayMiddleware: 中间件实例
        """
        # 获取配置的下载延迟
        delay = crawler.settings.get('DOWNLOAD_DELAY', 10)
        return cls(delay)
    
    def process_request(self, request, spider):
        """处理请求
        
        添加随机延迟
        
        Args:
            request: 请求对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续处理请求
        """
        # 添加随机延迟，基础延迟的0.5-1.5倍
        delay = self.delay * random.uniform(0.5, 1.5)
        spider.logger.debug(f"请求 {request.url} 添加随机延迟 {delay:.2f} 秒")
        time.sleep(delay)
        return None


class UserAgentMiddleware:
    """User-Agent中间件
    
    为请求随机添加User-Agent，降低被反爬的风险
    """
    def __init__(self):
        """初始化User-Agent中间件"""
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
        ]
        
    @classmethod
    def from_crawler(cls, crawler):
        """创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            UserAgentMiddleware: 中间件实例
        """
        middleware = cls()
        crawler.signals.connect(middleware.spider_opened, signal=signals.spider_opened)
        return middleware
    
    def process_request(self, request, spider):
        """处理请求
        
        为请求随机添加User-Agent
        
        Args:
            request: 请求对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续处理请求
        """
        # 如果请求中已经有User-Agent，则不覆盖
        if 'User-Agent' not in request.headers:
            # 随机选择一个User-Agent
            user_agent = random.choice(self.user_agents)
            request.headers['User-Agent'] = user_agent
            spider.logger.debug(f"为请求添加User-Agent: {user_agent}")
        
        return None
    
    def spider_opened(self, spider):
        """爬虫开启时调用
        
        Args:
            spider: 爬虫实例
        """
        spider.logger.info('UserAgentMiddleware已启用')


class RequestLogMiddleware:
    """请求日志中间件
    
    记录请求信息，方便调试
    """
    @classmethod
    def from_crawler(cls, crawler):
        """创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            RequestLogMiddleware: 中间件实例
        """
        return cls()
    
    def process_request(self, request, spider):
        """处理请求
        
        记录请求信息
        
        Args:
            request: 请求对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续处理请求
        """
        # 仅在DEBUG级别记录请求信息，避免日志过多
        if logging.DEBUG >= spider.logger.level:
            spider.logger.debug(f"请求: {request.url}")
            spider.logger.debug(f"头部: {request.headers}")
        return None
    
    def process_response(self, request, response, spider):
        """处理响应
        
        记录响应信息
        
        Args:
            request: 请求对象
            response: 响应对象
            spider: 爬虫实例
            
        Returns:
            Response: 返回响应对象，表示继续处理响应
        """
        # 仅在DEBUG级别记录响应信息
        if logging.DEBUG >= spider.logger.level:
            spider.logger.debug(f"响应: {response.url}, 状态: {response.status}")
        return response


class ProxyMiddleware:
    """代理中间件
    
    为请求添加代理，用于分散请求来源
    该中间件仅在settings中设置了PROXY_POOL时启用
    """
    def __init__(self, proxy_pool):
        """初始化代理中间件
        
        Args:
            proxy_pool: 代理池列表
        """
        self.proxy_pool = proxy_pool
        self.enabled = len(proxy_pool) > 0
        self.current_proxy = None
        self.fail_count = 0
    
    @classmethod
    def from_crawler(cls, crawler):
        """创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            ProxyMiddleware: 中间件实例
        """
        proxy_pool = crawler.settings.getlist('PROXY_POOL', [])
        return cls(proxy_pool)
    
    def process_request(self, request, spider):
        """处理请求
        
        添加代理
        
        Args:
            request: 请求对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续处理请求
        """
        if not self.enabled:
            return None
        
        # 如果没有当前代理或失败次数过多，选择新代理
        if not self.current_proxy or self.fail_count >= 3:
            self.current_proxy = random.choice(self.proxy_pool)
            self.fail_count = 0
            
        # 设置代理
        request.meta['proxy'] = self.current_proxy
        spider.logger.debug(f"使用代理: {self.current_proxy}")
        
        return None
    
    def process_response(self, request, response, spider):
        """处理响应
        
        检查代理是否有效
        
        Args:
            request: 请求对象
            response: 响应对象
            spider: 爬虫实例
            
        Returns:
            Response: 返回响应对象，表示继续处理响应
        """
        if not self.enabled:
            return response
        
        # 检查响应状态，如果不是2xx，记录失败
        if response.status < 200 or response.status >= 300:
            self.fail_count += 1
            spider.logger.warning(f"代理请求失败: {self.current_proxy}, 状态码: {response.status}")
            
        return response
    
    def process_exception(self, request, exception, spider):
        """处理异常
        
        记录代理异常
        
        Args:
            request: 请求对象
            exception: 异常对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续抛出异常
        """
        if not self.enabled:
            return None
        
        # 记录代理异常
        self.fail_count += 1
        spider.logger.warning(f"代理异常: {self.current_proxy}, 异常: {exception}")
        
        return None 


class CookieMiddleware:
    """Cookie中间件
    
    为请求添加Cookie，用于模拟登录状态
    """
    def __init__(self, settings):
        """初始化Cookie中间件
        
        Args:
            settings: 爬虫设置
        """
        self.logger = logging.getLogger(__name__)
        self.settings = settings
        self.cookie = settings.get('DEFAULT_REQUEST_HEADERS', {}).get('cookie', '')

        
        # 微博爬虫只从settings传入参数，不使用环境变量
        # 所有配置都应该通过settings.py进行管理
        
        if self.cookie:
            self.logger.info(f"Cookie中间件初始化，Cookie长度: {len(self.cookie)}")
        else:
            self.logger.warning("Cookie中间件初始化，未找到Cookie配置")
    
    @classmethod
    def from_crawler(cls, crawler):
        """创建中间件实例
        
        Args:
            crawler: 爬虫实例
            
        Returns:
            CookieMiddleware: 中间件实例
        """
        middleware = cls(crawler.settings)
        crawler.signals.connect(middleware.spider_opened, signal=signals.spider_opened)
        return middleware
    
    def process_request(self, request, spider):
        """处理请求
        
        为请求添加Cookie
        
        Args:
            request: 请求对象
            spider: 爬虫实例
            
        Returns:
            None: 返回None表示继续处理请求
        """
        # 优先使用爬虫实例的Cookie
        if hasattr(spider, 'cookie') and spider.cookie:
            request.headers['cookie'] = spider.cookie
            spider.logger.debug(f"使用爬虫实例的Cookie")
        # 其次使用中间件的Cookie
        elif self.cookie:
            request.headers['cookie'] = self.cookie
            spider.logger.debug(f"使用配置的Cookie")
        
        return None
    
    def spider_opened(self, spider):
        """爬虫开启时调用
        
        Args:
            spider: 爬虫实例
        """
        spider.logger.info(f"Cookie中间件已启用，{'已' if (hasattr(spider, 'cookie') and spider.cookie) or self.cookie else '未'}设置Cookie")
