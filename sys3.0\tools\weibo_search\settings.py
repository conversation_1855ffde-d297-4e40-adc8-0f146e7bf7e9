"""
微博爬虫设置

定义爬虫的行为配置
"""

import os
from pathlib import Path

# 获取项目根目录（sys3.0目录）
BASE_DIR = Path(__file__).parent.parent.parent
# 数据目录 - 确保在sys3.0内部
DATA_DIR = BASE_DIR / "data"
# 原始数据目录
RAW_DATA_DIR = DATA_DIR / "raw"
# 状态数据目录
STATUS_DIR = BASE_DIR / "logs" / "status"
    
# 爬虫基本设置
BOT_NAME = 'weibo'  # 爬虫名称
SPIDER_MODULES = ['tools.weibo_search.spiders']  # 爬虫模块位置
NEWSPIDER_MODULE = 'tools.weibo_search.spiders'  # 新爬虫模块位置

# Scrapy框架特定配置
COOKIES_ENABLED = False  # 禁用Scrapy的Cookie处理，改为在每个请求中手动设置Cookie
TELNETCONSOLE_ENABLED = False  # 是否启用telnet控制台
LOG_LEVEL = 'INFO'  # 日志级别
LOG_FILE = str(BASE_DIR / "logs" / "weibo_spider.log")  # 日志文件路径
DOWNLOAD_DELAY = 5  # 访问完一个页面再访问下一个时需要等待的时间，默认为10秒
ROBOTSTXT_OBEY = False  # 是否遵守robots.txt规则

# 默认请求头配置
DEFAULT_REQUEST_HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
    'Accept-Encoding': 'gzip, deflate, br',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Referer': 'https://weibo.com/',
    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-site',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'cookie': 'SCF=Am8FtrZz4V_2hzfigHKtxXEd-Hzy62nv45GT5hxzJXWGpe9JOrhvM1hRbZNd6WrZXs6vmzx0PfGKXMwr6xGFUDM.; SINAGLOBAL=423376186302.83514.1741053319334; UOR=,,www.yichang.gov.cn; ULV=1751423622666:13:1:1:6376409469340.045.1751423622452:1751080794830; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9WF6Mj_xc4SOGJjJA-fciP1S5JpX5KMhUgL.FoMX1he01hzESoz2dJLoI7D0qc8E9gBEeh5p; ALF=1756086323; SUB=_2A25FgENjDeRhGeFK41ES-CzOzT6IHXVm_NqrrDV8PUJbkNAbLXjukW1NQvIvKn6aBv_ZAte-SqALwsYicQpmYXJ-; XSRF-TOKEN=7wcfmDoQQuUrMiUh8eCuEIsq; WBPSESS=b9tkHRqraosXN5KoeDZ7ZY3TT7weKN-hfeUWHgVZLRmVebgMiUWGz11FvAKL7ldak2zBJ1AWlsBk2i7vMwM421Y54LOGFy0TJxBQpHeJqiiNUBOg9ME1-jpee-8_j8lq6gM0Tv1gSBEuBAHbdE3tkA=='  # 请在运行时通过命令行参数或API参数传入
}

# 中间件配置
DOWNLOADER_MIDDLEWARES = {
    'tools.weibo_search.middlewares.UserAgentMiddleware': 543,
    'tools.weibo_search.middlewares.CookieMiddleware': 544,
}

# 项目管道配置
ITEM_PIPELINES = {
    'tools.weibo_search.pipelines.DuplicatesPipeline': 300,
    # 'tools.weibo_search.pipelines.MongoPipeline': 303,
    #'tools.weibo_search.pipelines.MyImagesPipeline': 304,
    #'tools.weibo_search.pipelines.MyVideoPipeline': 305,
    'tools.weibo_search.pipelines.JsonWriterPipeline': 306,
}

# 微博爬虫特定配置
# 建筑安全隐患关键词配置 - 分层次搜索策略
KEYWORD_LIST = [
    # 结构安全隐患
    "建筑安全隐患", "结构安全隐患", "承重结构", "地基沉降", "地基不均匀沉降",
    "墙体开裂", "楼板裂缝", "梁柱开裂", "承重墙", "结构变形", "建筑倾斜",
    "基础沉降", "结构老化", "钢筋锈蚀", "混凝土碳化", "抗震不足", "承载力不足",

    # 消防安全隐患
    "消防安全隐患", "火灾隐患", "消防通道堵塞", "安全出口", "疏散通道",
    "消防设施", "消防栓", "烟感器", "喷淋系统", "防火门", "消防车道",
    "易燃材料", "防火分区", "消防验收", "消防检查", "消防整改",

    # 电气安全隐患
    "电气安全隐患", "电线老化", "配电设备", "漏电保护", "接地系统", "绝缘老化",
    "超负荷用电", "私拉乱接", "配电箱", "电气设备", "防雷设施", "静电",
    "电气火灾", "短路", "漏电", "用电安全", "电气检测",

    # 设备设施隐患
    "设备安全隐患", "电梯安全", "电梯故障", "供水系统", "排水系统", "通风系统",
    "空调系统", "燃气设施", "燃气泄漏", "锅炉设备", "压力容器", "特种设备",
    "管道老化", "阀门故障", "设备检测", "设备维护",

    # 建筑外围护隐患
    "外墙安全隐患", "外墙脱落", "外墙渗漏", "玻璃幕墙", "石材幕墙", "外保温",
    "外墙装饰", "广告牌", "空调外机", "防护栏杆", "阳台护栏", "窗户安全",
    "屋面防水", "屋面排水", "女儿墙", "檐口",

    # 室内环境隐患
    "室内安全隐患", "室内空气质量", "甲醛超标", "苯系物", "TVOC", "氨气",
    "石棉", "放射性", "噪声污染", "采光不足", "通风不良", "湿度过高",
    "霉菌", "细菌", "装修污染", "建材污染",

    # 地质环境隐患
    "地质灾害", "滑坡", "泥石流", "地面沉降", "地裂缝", "岩溶塌陷",
    "软土地基", "湿陷性黄土", "膨胀土", "冻土", "地下水", "土壤污染",
    "地震", "活断层", "地质勘察", "地基处理",

    # 自然灾害隐患
    "自然灾害隐患", "台风", "暴雨", "洪水", "雷击", "冰雹", "大雪",
    "高温", "严寒", "干旱", "沙尘暴", "抗风能力", "防洪标准",
    "排水能力", "应急预案", "灾害防护",

    # 使用功能隐患
    "功能安全隐患", "无障碍设施", "安全标识", "应急照明", "疏散指示",
    "防滑措施", "防撞设施", "儿童安全", "老人安全", "残疾人通道",
    "楼梯安全", "扶手", "台阶", "坡道", "门禁系统",

    # 管理维护隐患
    "管理安全隐患", "物业管理", "日常维护", "定期检查", "安全巡查", "维修保养",
    "档案管理", "应急管理", "人员培训", "安全制度", "责任落实",
    "隐患排查", "整改措施", "验收标准", "监督检查",

    # 违法违规隐患
    "违法建设", "违规改造", "擅自拆改", "超载使用", "改变用途", "无证建设",
    "违规装修", "私搭乱建", "占用消防通道", "堵塞安全出口", "违规用电",
    "违规用气", "违规储存", "违规经营", "监管缺失"
]  # 按建筑物本身存在的安全隐患类型分类
WEIBO_TYPE = 0               # 要搜索的微博类型，0代表搜索全部微博，1代表搜索全部原创微博，2代表热门微博，3代表关注人微博，4代表认证用户微博，5代表媒体微博，6代表观点微博
CONTAIN_TYPE = 0           # 筛选结果微博中必需包含的内容，0代表不筛选，获取全部微博，1代表搜索包含图片的微博，2代表包含视频的微博，3代表包含音乐的微博，4代表包含短链接的微博
REGION = ['全部']                # 筛选微博的发布地区，精确到省或直辖市，值不应包含"省"或"市"等字，如想筛选北京市的微博请用"北京"而不是"北京市"，想要筛选安徽省的微博请用"安徽"而不是"安徽省"，可以写多个地区， 具体支持的地名见region.py文件，注意只支持省或直辖市的名字，省下面的市名及直辖市下面的区县名不支持，不筛选请用"全部"
START_DATE = '2025-07-01'    # 搜索的起始日期，为yyyy-mm-dd形式，搜索结果包含该日期
END_DATE = '2025-07-24'        # 搜索的终止日期，为yyyy-mm-dd形式，搜索结果包含该日期
FURTHER_THRESHOLD = 46  # 进一步细分搜索的阈值，若结果页数大于等于该值，则认为结果没有完全展示，细分搜索条件重新搜索以获取更多微博。数值越大速度越快，也越有可能漏掉微博；数值越小速度越慢，获取的微博就越多。 建议数值大小设置在40到50之间。

# 数据存储配置
# 默认微博数据保存目录
WEIBO_DATA_DIR = str(RAW_DATA_DIR / 'weibo')  # 微博数据保存目录

# 图片文件存储路径
IMAGES_STORE = os.path.join(WEIBO_DATA_DIR, 'images')        # 图片存储路径
# 视频文件存储路径
FILES_STORE = os.path.join(WEIBO_DATA_DIR, 'videos')         # 视频存储路径

# 配置MongoDB数据库
MONGO_URI = 'localhost:27017' # MongoDB连接URI
MONGO_DB = 'weibo'           # MongoDB数据库名称

# 请求超时和重试配置
DOWNLOAD_TIMEOUT = 10  # 下载超时时间
RETRY_ENABLED = True  # 是否启用重试
RETRY_TIMES = 3       # 最大重试次数
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]  # 需要重试的HTTP状态码

# 禁止遵循robots.txt规则
ROBOTSTXT_OBEY = False

# 请求指纹实现配置
REQUEST_FINGERPRINTER_IMPLEMENTATION = "2.7"
FEED_EXPORT_ENCODING = "utf-8"
