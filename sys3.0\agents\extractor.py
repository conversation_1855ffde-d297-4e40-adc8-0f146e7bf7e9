"""
提取器智能体模块
负责从原始数据中提取结构化信息，基于CrewAI框架实现
"""

# 标准库导入
import hashlib
import time
from datetime import datetime
from typing import Dict, Any, Optional

# 本地应用导入
from .base_agent import BaseAgent
from utils.data_service import data_service
from utils.exceptions import safe_execute, SystemException
from utils.progress import create_progress_bar

class ExtractorAgent(BaseAgent):
    """
    信息提取智能体
    负责从原始数据中提取与建筑安全隐患相关的结构化信息
    基于CrewAI框架实现
    """
    
    def __init__(
        self,
        name: str = "信息提取者",
        role: str = "建筑安全信息提取专家",
        goal: str = "从原始数据中提取结构化的建筑安全隐患信息",
        backstory: Optional[str] = None,
        llm_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False
    ):
        """初始化信息提取智能体"""
        backstory = backstory or "专业的建筑安全信息提取专家，负责从原始数据中提取结构化的安全隐患信息。"

        super().__init__(
            name=name,
            role=role,
            goal=goal,
            backstory=backstory,
            llm_config=llm_config,
            verbose=verbose
        )

    def _should_extract_content(self, content: str, publish_time: str) -> dict:
        """
        智能语义预判断：多维度分析内容是否值得抽取

        Args:
            content: 微博文本内容
            publish_time: 发布时间

        Returns:
            dict: 包含是否抽取和分析结果的字典
        """
        # 快速过滤明显无关的内容
        if len(content) < 20:
            return {"should_extract": False, "reason": "内容过短", "confidence": 1.0}

        # 直接使用LLM分析，不依赖硬编码规则
        llm_result = self._llm_semantic_analysis(content, publish_time)

        self.log_info(f"LLM语义分析结果: {llm_result}")
        return llm_result

    # 硬编码规则方法已删除，直接使用LLM进行智能分析

    def _llm_semantic_analysis(self, content: str, publish_time: str) -> dict:
        """增强的LLM语义分析 - 通用性和鲁棒性更强"""

        analysis_prompt = f"""
你是建筑安全领域的专家，请对以下微博内容进行专业分析，判断是否为需要处理的建筑安全隐患举报。

微博内容：{content}
发布时间：{publish_time}

## 核心判断原则

**应该提取的内容（建筑安全隐患举报）：**
1. 描述当前存在的具体建筑安全问题
2. 涉及建筑结构、施工安全、消防安全、电气安全等建筑相关安全问题
3. 提供了具体的位置信息或可定位的描述
4. 是实际存在的问题，不是假设或历史案例
5. 可以通过实地检查或整改措施解决

**不应该提取的内容：**
1. **非建筑安全领域**：动物保护、环境保护、食品安全、交通安全、网络安全、医疗健康、教育娱乐等其他领域的安全问题
2. **教育培训内容**：安全培训、安全教育、知识普及、安全宣传、培训课程、案例讲解、消防演练等教学活动
3. **已解决问题**：明确表示已经解决、已整改、已修复、已完成、处理完毕、验收通过等
4. **工作汇报**：工作计划、管理制度、培训安排、会议通知、工作总结、政策解读等行政管理内容
5. **一般性呼吁**：统计数据、泛泛而谈、观点表达、政策建议、社会评论等非具体举报
6. **历史事件**：过去发生的事故分析、历史案例回顾、新闻报道等非当前问题

## 特别注意识别

**容易混淆的情况：**
- 培训中提到的案例 → 不是实际隐患举报
- 新闻报道中的事故 → 通常不是当前需要处理的隐患
- 工作人员的日常汇报 → 不是隐患举报
- 安全知识科普 → 不是具体问题举报
- 其他领域的"安全"问题 → 仔细判断是否属于建筑安全

**判断技巧：**
- 看是否有具体的地点描述
- 看是否是当前存在的问题
- 看是否属于建筑安全范畴
- 看是否可以实地处理

请分析以下维度并给出评分（0-1）：
1. domain_relevance: 是否属于建筑安全领域（非动物保护、环境保护等其他领域）
2. hazard_existence: 是否描述了实际存在的安全隐患（非教育案例、历史事件）
3. current_status: 是当前存在问题（1）还是历史/已解决问题（0）
4. specificity: 描述的具体性（具体位置、具体问题）
5. actionability: 是否可采取实地行动处理
6. content_nature: 是具体事件举报（1）还是一般性内容（0）

请以JSON格式返回：
{{
    "should_extract": true/false,
    "domain_relevance": 0.0-1.0,
    "hazard_existence": 0.0-1.0,
    "current_status": 0.0-1.0,
    "specificity": 0.0-1.0,
    "actionability": 0.0-1.0,
    "content_nature": 0.0-1.0,
    "overall_confidence": 0.0-1.0,
    "content_type": "concrete_hazard/education/resolved_issue/work_report/general_appeal/non_construction/other",
    "reason": "详细说明判断理由，包括关键判断依据"
}}
        """

        try:
            result = self.call_llm(analysis_prompt)
            if result:
                # 尝试解析JSON
                from utils.data_service import data_service
                analysis = data_service.extract_json_from_text(str(result))
                if analysis:
                    return analysis

            # 如果解析失败，默认不抽取
            return {"should_extract": False, "reason": "LLM分析失败", "confidence": 0.3}

        except Exception as e:
            self.log_warning(f"LLM语义分析失败: {e}")
            return {"should_extract": False, "reason": "分析失败", "confidence": 0.3}

    # _combine_analysis_results方法已删除，直接使用LLM分析结果

    def _validate_extraction_quality(self, original_content: str, extracted_data: dict, analysis_result: dict) -> dict:
        """
        验证抽取结果的质量

        Args:
            original_content: 原始微博内容
            extracted_data: 抽取的结构化数据
            analysis_result: 语义分析结果

        Returns:
            dict: 质量评估结果
        """
        issues = []
        score = 1.0

        # 1. 基础字段完整性检查
        required_fields = ["hazard_description", "location", "timestamp"]
        for field in required_fields:
            if not extracted_data.get(field):
                issues.append(f"缺少{field}字段")
                score -= 0.2

        # 2. 内容一致性检查
        description = extracted_data.get("hazard_description", "")
        if len(description) < 10:
            issues.append("隐患描述过短")
            score -= 0.2
        elif len(description) > len(original_content) * 0.8:
            issues.append("描述可能包含过多原文内容")
            score -= 0.1

        # 3. 时间合理性检查
        timestamp = extracted_data.get("timestamp", "")
        try:
            from datetime import datetime
            # 支持多种时间格式
            time_formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y-%m-%d"
            ]

            parsed_time = None
            for fmt in time_formats:
                try:
                    parsed_time = datetime.strptime(timestamp, fmt)
                    break
                except ValueError:
                    continue

            if parsed_time is None:
                issues.append("时间格式错误")
                score -= 0.2
            else:
                current_time = datetime.now()
                if parsed_time > current_time:
                    issues.append("时间不能是未来时间")
                    score -= 0.3
        except Exception:
            issues.append("时间格式错误")
            score -= 0.2

        # 4. 地理位置合理性检查
        location = extracted_data.get("location", "")
        if location and len(location) < 3:
            issues.append("地理位置信息过于简单")
            score -= 0.1

        # 5. 利用语义分析结果
        if "analysis" in analysis_result:
            semantic_analysis = analysis_result["analysis"]
            if semantic_analysis.get("specificity", 0) < 0.5:
                issues.append("描述缺乏具体性")
                score -= 0.1
            if semantic_analysis.get("actionability", 0) < 0.5:
                issues.append("缺乏可操作性")
                score -= 0.1

        # 确保分数在0-1范围内
        score = max(0.0, min(1.0, score))

        return {
            "score": score,
            "issues": issues,
            "level": "high" if score >= 0.8 else "medium" if score >= 0.6 else "low"
        }

    def _extract_with_retry(self, content: str, metadata: dict, max_retries: int = 2) -> dict:
        """
        带重试机制的抽取方法

        Args:
            content: 微博内容
            metadata: 元数据
            max_retries: 最大重试次数

        Returns:
            dict: 抽取结果
        """
        for attempt in range(max_retries + 1):
            try:
                result = self.extract_hazard_info(content, metadata)

                # 检查是否为跳过标记
                if isinstance(result, dict) and result.get("skip"):
                    self.log_info(f"语义预判断跳过，不进行重试: {result.get('reason')}")
                    return result

                # 检查是否成功抽取
                if result:  # 成功抽取
                    return result
                elif attempt < max_retries:  # 真正的抽取失败，需要重试
                    self.log_warning(f"抽取失败，第{attempt + 1}次重试")
                    continue
                else:  # 最后一次尝试也失败
                    return None

            except Exception as e:
                if attempt < max_retries:
                    self.log_warning(f"抽取异常，第{attempt + 1}次重试: {e}")
                    continue
                else:
                    self.log_error(f"抽取最终失败: {e}")
                    return None

        return None

    def extract_hazard_info(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从文本内容中提取建筑安全隐患信息

        Args:
            content: 文本内容
            metadata: 元数据，如发布时间、地点等

        Returns:
            Dict[str, Any]: 提取的隐患信息，符合新的数据结构
        """
        self.log_info(f"从文本中提取建筑安全隐患信息，文本长度: {len(content)}")

        # 确保metadata不为None
        metadata = metadata or {}

        # 从metadata中提取所需信息 - 使用新的WeiboItem字段
        publish_time = metadata.get("timestamp", "")
        location = metadata.get("location", "未知")
        weibo_id = metadata.get("weibo_id", "")
        weibo_url = metadata.get("weibo_url", "")
        author = metadata.get("author", "")

        # 第一步：智能语义预判断 - 避免无效抽取
        analysis_result = self._should_extract_content(content, publish_time)
        if not analysis_result["should_extract"]:
            self.log_info(f"语义预判断：跳过抽取 - {analysis_result['reason']}")
            # 返回特殊标记，表示正常跳过，不需要重试
            return {
                "skip": True,
                "reason": analysis_result['reason'],
                "analysis": analysis_result
            }
        
        # 创建改进的任务描述
        task_description = f"""
你是建筑安全专家。请从以下微博中提取**当前存在**的建筑安全隐患信息。

微博内容：
```
{content}
```

发布时间：{publish_time}
发布者：{author}

重要提醒：
1. 只提取**当前正在发生**的安全隐患，忽略已解决的历史问题
2. timestamp字段使用微博发布时间：{publish_time}
3. 保留原文中的重要细节，不要过度简化
4. 如果文本中提到地名错误（如"刑台"），可以合理纠正为"邢台"

请提取以下字段：
- timestamp: 使用微博发布时间 {publish_time}
- location: 具体地点（从文本中提取，尽可能详细）
- hazard_description: 隐患详细描述（保留重要细节，不要过度简化）
- has_hazard: 是否包含当前存在的建筑安全隐患 (true/false)
- hazard_type: 隐患类型（如"违规搭建"、"消防隐患"、"结构安全"等）
- images: 图片信息列表
- videos: 视频信息列表

请以JSON格式输出。"""
        
        # 使用统一的LLM调用方法
        try:
            result = self.call_llm(task_description)

            if not result:
                self.log_error("LLM调用无结果")
                return None

        except Exception as e:
            self.log_error(f"LLM调用失败: {str(e)}")
            return None
        
        # 解析结果
        try:
            # 尝试从结果中提取JSON，使用数据服务的统一方法
            extracted_data = data_service.extract_json_from_text(str(result))
            if not extracted_data:
                self.log_warning("无法从响应中解析JSON数据")
                return {}
                
            # 检查是否包含安全隐患
            if not extracted_data.get("has_hazard", False):
                self.log_info("微博内容不包含建筑安全隐患")
                return {"has_hazard": False}
            
            # 处理时间信息
            timestamp = extracted_data.get("timestamp")
            if not timestamp and publish_time:
                # 使用原始发布时间
                timestamp = publish_time
            elif not timestamp:
                # 使用当前时间
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 获取地点信息
            location_info = extracted_data.get("location", location)

            # 获取描述信息
            hazard_description = extracted_data.get("hazard_description", "")
            
            # 生成隐患ID
            # 使用更安全的方式生成唯一ID，避免hash函数的不确定性
            content_for_hash = f"{hazard_description}_{weibo_id}_{timestamp}"
            hash_value = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:6]
            hazard_id = f"HZ-{datetime.now().strftime('%Y%m%d')}-{hash_value}"

            # 创建隐患数据 - 按照新的HazardItem格式
            hazard_info = {
                "hazard_id": hazard_id,
                "weibo_id": weibo_id,
                "weibo_url": weibo_url,
                "timestamp": timestamp,
                "location": location_info,
                "author": author,
                "hazard_description": hazard_description,
                "images": [],
                "videos": []
            }

            # 添加图像信息 - 使用MediaItem格式
            image_urls = metadata.get("images", [])
            for url in image_urls:
                hazard_info["images"].append({
                    "url": url,
                    "description": None  # 预留字段，暂不实现
                })

            # 添加视频信息 - 使用MediaItem格式
            video_urls = metadata.get("videos", [])
            for url in video_urls:
                hazard_info["videos"].append({
                    "url": url,
                    "description": None  # 预留字段，暂不实现
                })
            
            # 第三步：质量验证
            quality_result = self._validate_extraction_quality(content, hazard_info, analysis_result)
            hazard_info["quality_score"] = quality_result["score"]
            hazard_info["quality_issues"] = quality_result["issues"]

            self.log_info(f"成功提取隐患信息: {hazard_id}, 质量评分: {quality_result['score']:.2f}")
            return hazard_info
                
        except Exception as e:
            self.log_error(f"解析提取结果失败: {e}")
            return {}
    
    def check_hazard_completeness(self, hazard_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查隐患信息是否完整
        
        Args:
            hazard_info: 隐患信息
            
        Returns:
            Dict[str, Any]: 更新后的隐患信息，包含完整性标记
        """
        # 必须字段列表，根据新的HazardItem格式定义
        required_fields = ["timestamp", "location", "hazard_description"]
        
        # 检查必须字段是否存在且非空
        missing = []
        for field in required_fields:
            if field not in hazard_info or not hazard_info[field] or (isinstance(hazard_info[field], str) and hazard_info[field].strip() == ""):
                missing.append(field)
        
        # 更新隐患信息
        hazard_info["is_complete"] = len(missing) == 0
        hazard_info["missing_fields"] = missing if missing else None
        
        return hazard_info
    
    @safe_execute
    def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行信息提取任务
        
        Args:
            task_input: 任务输入，包含以下字段
                - data: 微博数据列表
                - input_file: 输入文件路径（可选，与data二选一）
                - task_id: 任务ID，可选
                
        Returns:
            Dict[str, Any]: 任务输出，包含结构化的隐患信息
        """
        self.log_info("开始执行信息提取任务")
        
        # 获取任务ID
        task_id = task_input.get("task_id", data_service.generate_task_id())
        
        # 获取微博数据
        weibo_data = []
        
        # 如果提供了data字段，直接使用
        if "data" in task_input and task_input["data"]:
            weibo_data = task_input["data"]
            self.log_info(f"从任务输入获取到 {len(weibo_data)} 条微博数据")
        # 如果提供了input_file字段，从文件加载数据
        elif "input_file" in task_input and task_input["input_file"]:
            input_file = task_input["input_file"]
            self.log_info(f"从文件加载微博数据: {input_file}")
            try:
                weibo_data = data_service.load_json(input_file)
                self.log_info(f"从文件加载了 {len(weibo_data)} 条微博数据")
            except Exception as e:
                self.log_error(f"加载微博数据失败: {e}")
                return {
                    "status": "error",
                    "error": f"加载微博数据失败: {str(e)}",
                    "task_id": task_id
                }
        else:
            self.log_error("缺少必要参数: data 或 input_file")
            return {
                "status": "error", 
                "error": "缺少必要参数: data 或 input_file",
                "task_id": task_id
            }
        
        # 如果输入为空
        if not weibo_data:
            self.log_warning("输入数据为空")
            return {"status": "success", "data": [], "message": "输入数据为空", "task_id": task_id}
            
        # 更新任务状态为运行中
        data_service.update_task_status(task_id, "extract", "running", {
            "start_time": datetime.now().isoformat(),
            "input_count": len(weibo_data)
        })
        
        # 记录处理开始时间
        start_time = time.time()

        # 初始化进度条
        progress_bar = create_progress_bar(
            total=len(weibo_data),
            stage_name="信息抽取",
            logger=self.logger,
            update_threshold=2  # 每2%显示一次进度
        )

        # 提取结构化信息
        structured_data = []
        processed_count = 0
        skipped_count = 0
        low_quality_count = 0
        error_count = 0
            
        for idx, weibo in enumerate(weibo_data):
            # 确保weibo是字典类型且符合WeiboItem格式
            if isinstance(weibo, str):
                # 如果是字符串，转换为WeiboItem格式
                weibo = {"content": weibo, "weibo_id": f"str_{idx}", "weibo_url": "", "timestamp": "", "location": "", "author": "", "images": [], "videos": []}
            elif not isinstance(weibo, dict):
                # 如果不是字典也不是字符串，跳过
                continue

            # 获取微博内容 - 支持新旧字段名
            content = weibo.get("content", "") or weibo.get("text", "")
            if not content:
                continue

            # 提取信息 - 传入完整的WeiboItem数据，增加重试机制
            hazard_info = self._extract_with_retry(content, weibo, max_retries=2)

            # 处理不同的返回情况
            if hazard_info:
                # 检查是否为跳过标记
                if hazard_info.get("skip"):
                    # 跳过的内容不计入结果，但记录统计
                    skipped_count += 1
                else:
                    # 正常的抽取结果，进行质量检查
                    quality_score = hazard_info.get("quality_score", 0.5)
                    if quality_score >= 0.6:  # 只添加质量较高的数据
                        structured_data.append(hazard_info)
                    else:
                        self.log_warning(f"质量评分过低({quality_score:.2f})，跳过添加: {hazard_info.get('hazard_id')}")
                        low_quality_count += 1
            
            processed_count += 1

            # 更新进度条
            progress_bar.update(current=processed_count)

            # 更新任务状态（减少频率，每处理20条或完成时更新）
            if processed_count % 20 == 0 or processed_count == len(weibo_data):
                data_service.update_task_status(task_id, "extract", "running", {
                    "processed_count": processed_count,
                    "total_count": len(weibo_data),
                    "extracted_count": len(structured_data),
                    "skipped_count": skipped_count,
                    "low_quality_count": low_quality_count,
                    "progress": f"{processed_count}/{len(weibo_data)}"
                })
        
        # 完成进度条
        progress_bar.finish()

        # 处理时间和统计信息
        process_time = time.time() - start_time
        success_rate = len(structured_data) / processed_count * 100 if processed_count > 0 else 0

        self.log_info(f"""
信息提取完成统计:
- 处理微博数: {processed_count}
- 成功提取: {len(structured_data)} 条隐患
- 跳过抽取: {skipped_count} 条 (语义预判断)
- 低质量过滤: {low_quality_count} 条
- 错误失败: {error_count} 条
- 成功率: {success_rate:.1f}%
- 处理耗时: {process_time:.2f} 秒
- 平均耗时: {process_time/processed_count:.2f} 秒/条
        """)
        
        # 定义输出文件路径
        output_path = self.get_path("structured_data_dir") / f"{task_id}.json"
                
        # 保存结构化数据（包括空结果）
        try:
            # 保存到结构化数据目录
            data_service.save_json(structured_data, output_path)
            self.log_info(f"结构化数据已保存到: {output_path}")

            # 同时保存到任务数据中
            data_service.save_task_data(task_id, "structured", structured_data)
            self.log_info(f"结构化数据已保存到任务: {task_id}")

            if not structured_data:
                self.log_warning("未提取到任何隐患信息，但已保存空结果")
        except Exception as e:
            self.log_error(f"保存结构化数据失败: {str(e)}")
            raise SystemException(f"保存结构化数据失败: {str(e)}")
        
        # 更新任务状态为完成
        data_service.update_task_status(task_id, "extract", "completed", {
            "end_time": datetime.now().isoformat(),
            "duration": f"{process_time:.2f}秒",
            "processed_count": processed_count,
            "extracted_count": len(structured_data),
            "skipped_count": skipped_count,
            "low_quality_count": low_quality_count
        })

        return {
            "status": "success",
            "data": structured_data,
            "task_id": task_id,
            "output_file": str(output_path) if structured_data else None,
            "structured_data_path": str(output_path) if structured_data else None,  # 添加structured_data_path字段
            "processed_count": processed_count,
            "extracted_count": len(structured_data),
            "skipped_count": skipped_count,  # 新增
            "low_quality_count": low_quality_count,  # 新增
            "duration": process_time
        }
            
    def __str__(self) -> str:
        """返回智能体描述"""
        return f"ExtractorAgent(name='{self.name}', role='{self.role}')" 
