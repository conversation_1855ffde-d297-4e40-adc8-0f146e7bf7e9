{"nodes": [{"id": "case_001", "label": "江西丰城发电厂\"11·24\"冷却塔施工平台坍塌特别重大事故调查报告", "type": "case", "case_id": "A001", "region": "江西省宜春市丰城市", "time": "2016-11-24"}, {"id": "hazard_001", "label": "违规施工", "type": "hazard_behavior"}, {"id": "warning_001", "label": "结构变形", "type": "warning_signal"}, {"id": "trigger_001", "label": "支撑体系失稳", "type": "trigger_cause"}, {"id": "damage_001", "label": "重大人员伤亡", "type": "damage"}, {"id": "case_002", "label": "凤凰县沱江大桥垮塌事故调查报告", "type": "case", "case_id": "A002", "region": "湖南省凤凰县", "time": "2007-08-13"}, {"id": "hazard_002", "label": "违规拆除作业", "type": "hazard_behavior"}, {"id": "warning_002", "label": "桥墩下沉", "type": "warning_signal"}, {"id": "trigger_002", "label": "桥体垮塌", "type": "trigger_cause"}, {"id": "case_003", "label": "北京丰台长峰医院\"4·18\"重大火灾事故调查报告", "type": "case", "case_id": "A003", "region": "北京市丰台区", "time": "2023-04-18"}, {"id": "hazard_003", "label": "电气线路敷设混乱", "type": "hazard_behavior"}, {"id": "warning_003", "label": "电线打火跳闸", "type": "warning_signal"}, {"id": "trigger_003", "label": "电气短路引发火灾", "type": "trigger_cause"}, {"id": "damage_003", "label": "火灾蔓延致人员伤亡", "type": "damage"}, {"id": "case_004", "label": "宁夏优品饰嘉建筑装饰工程有限公司\"10·14\"一般触电事故调查报告", "type": "case", "case_id": "A004", "region": "宁夏回族自治区银川市金凤区", "time": "2023-10-14"}, {"id": "hazard_004", "label": "带电作业", "type": "hazard_behavior"}, {"id": "warning_004", "label": "电线未绝缘封闭", "type": "warning_signal"}, {"id": "trigger_004", "label": "触碰裸露电线", "type": "trigger_cause"}, {"id": "damage_004", "label": "触电死亡", "type": "damage"}, {"id": "case_005", "label": "綦江隆盛江津—南川天然气输气管道工程三标段\"3·19\"一般坍塌事故调查报告", "type": "case", "case_id": "A005", "region": "重庆市綦江区隆盛镇", "time": "2023-03-19"}, {"id": "hazard_005", "label": "沟槽支护缺失", "type": "hazard_behavior"}, {"id": "warning_005", "label": "槽壁裂缝", "type": "warning_signal"}, {"id": "trigger_005", "label": "沟槽边坡坍塌", "type": "trigger_cause"}, {"id": "damage_005", "label": "作业人员被掩埋", "type": "damage"}, {"id": "case_006", "label": "柳州市东环大道商铺装修工地\"11·20\"物体打击事故调查报告", "type": "case", "case_id": "A006", "region": "广西壮族自治区柳州市鱼峰区", "time": "2023-11-20"}, {"id": "hazard_006", "label": "高处作业未封闭", "type": "hazard_behavior"}, {"id": "warning_006", "label": "物料滑落迹象", "type": "warning_signal"}, {"id": "trigger_006", "label": "砖块滑落", "type": "trigger_cause"}, {"id": "damage_006", "label": "物体打击致死", "type": "damage"}, {"id": "case_007", "label": "阳泉市燕竹花园四组团五期开发项目\"10·22\"物体打击事故调查报告", "type": "case", "case_id": "A007", "region": "山西省阳泉市城区", "time": "2023-10-22"}, {"id": "hazard_007", "label": "模板安装未加固", "type": "hazard_behavior"}, {"id": "warning_007", "label": "物料掉落", "type": "warning_signal"}, {"id": "trigger_007", "label": "模板滑脱坠落", "type": "trigger_cause"}, {"id": "case_008", "label": "南宁市金域中央四期工程工地\"4·15\"物体打击事故调查报告", "type": "case", "case_id": "A008", "region": "广西壮族自治区南宁市西乡塘区", "time": "2023-04-15"}, {"id": "hazard_008", "label": "脚手架未清理残留物", "type": "hazard_behavior"}, {"id": "warning_008", "label": "构件滑落记录", "type": "warning_signal"}, {"id": "trigger_008", "label": "扣件松动坠落", "type": "trigger_cause"}, {"id": "case_009", "label": "宁夏沁灏建筑安装有限公司\"3·05\"车辆伤害事故调查报告", "type": "case", "case_id": "A009", "region": "宁夏回族自治区银川市金凤区", "time": "2023-03-05"}, {"id": "hazard_009", "label": "人车混行", "type": "hazard_behavior"}, {"id": "warning_009", "label": "倒车未设引导", "type": "warning_signal"}, {"id": "trigger_009", "label": "车辆碾压", "type": "trigger_cause"}, {"id": "case_010", "label": "布尔津河南骉辉建设工程有限公司\"10·29\"高处坠落事故调查报告", "type": "case", "case_id": "A010", "region": "新疆维吾尔自治区阿勒泰地区布尔津县", "time": "2023-10-29"}, {"id": "hazard_010", "label": "拆除作业无防护", "type": "hazard_behavior"}, {"id": "warning_010", "label": "屋顶破损无标识", "type": "warning_signal"}, {"id": "trigger_010", "label": "踩踏松动板材坠落", "type": "trigger_cause"}, {"id": "damage_010", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_011", "label": "齐齐哈尔中学体育馆坍塌事故调查报告", "type": "case", "case_id": "A011", "region": "黑龙江省齐齐哈尔市建华区", "time": "2023-07-23"}, {"id": "hazard_011", "label": "违规加建采光板", "type": "hazard_behavior"}, {"id": "warning_011", "label": "屋顶下沉变形", "type": "warning_signal"}, {"id": "trigger_011", "label": "结构超载失稳", "type": "trigger_cause"}, {"id": "damage_011", "label": "体育馆整体坍塌", "type": "damage"}, {"id": "case_012", "label": "湖南衡阳\"11·3\"特大火灾坍塌事故调查报告", "type": "case", "case_id": "A012", "region": "湖南省衡阳市石鼓区", "time": "2003-11-03"}, {"id": "hazard_012", "label": "违规住人用电", "type": "hazard_behavior"}, {"id": "warning_012", "label": "电气线路打火", "type": "warning_signal"}, {"id": "trigger_012", "label": "电气短路引发火灾", "type": "trigger_cause"}, {"id": "damage_012", "label": "火灾后结构坍塌", "type": "damage"}, {"id": "case_013", "label": "深圳赛格大厦\"5·18\"异常晃动事件调查报告", "type": "case", "case_id": "A013", "region": "广东省深圳市福田区", "time": "2021-05-18"}, {"id": "hazard_013", "label": "通信设备长期振动", "type": "hazard_behavior"}, {"id": "warning_013", "label": "轻微震动感觉", "type": "warning_signal"}, {"id": "trigger_013", "label": "模态耦合共振", "type": "trigger_cause"}, {"id": "damage_013", "label": "建筑异常晃动", "type": "damage"}, {"id": "case_014", "label": "右江区太平街隆平巷79号民房\"8·5\"较大火灾事故调查报告", "type": "case", "case_id": "A014", "region": "广西壮族自治区百色市右江区", "time": "2023-08-05"}, {"id": "hazard_014", "label": "电气线路私拉乱接", "type": "hazard_behavior"}, {"id": "warning_014", "label": "电线老化起火冒烟", "type": "warning_signal"}, {"id": "trigger_014", "label": "电气线路故障引发火灾", "type": "trigger_cause"}, {"id": "damage_014", "label": "火势蔓延致人死亡", "type": "damage"}, {"id": "case_015", "label": "山东省龙口市东江建筑公司塔吊倾倒事故调查报告", "type": "case", "case_id": "A015", "region": "山东省烟台市龙口市", "time": "2008-03-31"}, {"id": "hazard_015", "label": "塔吊基础未按设计施工", "type": "hazard_behavior"}, {"id": "warning_015", "label": "塔吊摆动异常", "type": "warning_signal"}, {"id": "trigger_015", "label": "强风作用塔吊倾覆", "type": "trigger_cause"}, {"id": "damage_015", "label": "塔吊砸中休息区", "type": "damage"}, {"id": "case_016", "label": "北京市朝阳区某小区地下室电气施工触电事故调查报告", "type": "case", "case_id": "A016", "region": "北京市朝阳区", "time": "2023-08-07"}, {"id": "hazard_016", "label": "未断电作业", "type": "hazard_behavior"}, {"id": "warning_016", "label": "配电箱无挂牌断电", "type": "warning_signal"}, {"id": "trigger_016", "label": "误触带电铜排", "type": "trigger_cause"}, {"id": "damage_016", "label": "电击身亡", "type": "damage"}, {"id": "case_017", "label": "桦南县悦城广场体育馆部分坍塌事故调查报告", "type": "case", "case_id": "A017", "region": "黑龙江省佳木斯市桦南县", "time": "2021-07-25"}, {"id": "hazard_017", "label": "屋盖结构设计缺陷", "type": "hazard_behavior"}, {"id": "warning_017", "label": "屋顶挠度异常", "type": "warning_signal"}, {"id": "trigger_017", "label": "钢结构节点失效", "type": "trigger_cause"}, {"id": "damage_017", "label": "屋顶结构坍塌", "type": "damage"}, {"id": "case_018", "label": "长沙市望城区居民自建房倒塌事故调查报告", "type": "case", "case_id": "A018", "region": "湖南省长沙市望城区", "time": "2022-04-29"}, {"id": "hazard_018", "label": "违规加层改建", "type": "hazard_behavior"}, {"id": "warning_018", "label": "房屋晃动异常", "type": "warning_signal"}, {"id": "trigger_018", "label": "结构体系被破坏", "type": "trigger_cause"}, {"id": "damage_018", "label": "房屋整体倒塌", "type": "damage"}, {"id": "case_019", "label": "山西襄汾县聚仙饭店坍塌事故调查报告", "type": "case", "case_id": "A019", "region": "山西省临汾市襄汾县", "time": "2020-08-29"}, {"id": "hazard_019", "label": "违规改扩建", "type": "hazard_behavior"}, {"id": "warning_019", "label": "墙体裂缝屋顶变形", "type": "warning_signal"}, {"id": "trigger_019", "label": "结构失稳整体坍塌", "type": "trigger_cause"}, {"id": "damage_019", "label": "建筑物整体坍塌", "type": "damage"}, {"id": "case_020", "label": "广州海珠城广场工地\"7·21\"塌陷事故调查报告", "type": "case", "case_id": "A020", "region": "广东省广州市海珠区", "time": "2005-07-21"}, {"id": "hazard_020", "label": "基坑支护未按设计施工", "type": "hazard_behavior"}, {"id": "warning_020", "label": "基坑边坡裂缝", "type": "warning_signal"}, {"id": "trigger_020", "label": "基坑支护结构失稳", "type": "trigger_cause"}, {"id": "damage_020", "label": "大面积塌陷", "type": "damage"}, {"id": "case_021", "label": "湖南华容县华容明珠三期工程项目\"1·23\"塔式起重机坍塌事故", "type": "case", "case_id": "A021", "region": "湖南省岳阳市华容县", "time": "2019-01-23"}, {"id": "hazard_021", "label": "拆卸作业违规操作", "type": "hazard_behavior"}, {"id": "warning_021", "label": "拆卸作业缺乏监控", "type": "warning_signal"}, {"id": "trigger_021", "label": "结构失稳坍塌", "type": "trigger_cause"}, {"id": "damage_021", "label": "塔式起重机坍塌", "type": "damage"}, {"id": "case_022", "label": "上海市长宁区厂房\"5·16\"坍塌事故调查报告", "type": "case", "case_id": "A022", "region": "上海市长宁区", "time": "2019-05-16"}, {"id": "hazard_022", "label": "擅自拆除承重墙", "type": "hazard_behavior"}, {"id": "warning_022", "label": "墙体裂缝混凝土剥落", "type": "warning_signal"}, {"id": "trigger_022", "label": "上部荷载失衡", "type": "trigger_cause"}, {"id": "damage_022", "label": "楼板屋顶坍塌", "type": "damage"}, {"id": "case_023", "label": "汕尾市陆河县\"10·8\"建筑施工坍塌事故调查报告", "type": "case", "case_id": "A023", "region": "广东省汕尾市陆河县", "time": "2023-10-08"}, {"id": "hazard_023", "label": "高支模搭设不规范", "type": "hazard_behavior"}, {"id": "warning_023", "label": "支撑杆晃动变形", "type": "warning_signal"}, {"id": "trigger_023", "label": "支模体系失稳坍塌", "type": "trigger_cause"}, {"id": "damage_023", "label": "作业人员坠落被埋", "type": "damage"}, {"id": "case_024", "label": "重庆綦江区\"3·19\"天然气管道施工坍塌事故调查报告", "type": "case", "case_id": "A024", "region": "重庆市綦江区隆盛镇", "time": "2023-03-19"}, {"id": "hazard_024", "label": "沟槽开挖深度超标", "type": "hazard_behavior"}, {"id": "warning_024", "label": "沟壁裂缝土体松动", "type": "warning_signal"}, {"id": "trigger_024", "label": "边坡失稳坍塌", "type": "trigger_cause"}, {"id": "damage_024", "label": "作业人员被掩埋", "type": "damage"}, {"id": "case_025", "label": "河北衡水市翡翠华庭\"4·25\"施工升降机坠落事故调查报告", "type": "case", "case_id": "A025", "region": "河北省衡水市桃城区", "time": "2022-04-25"}, {"id": "hazard_025", "label": "升降机维护不到位", "type": "hazard_behavior"}, {"id": "warning_025", "label": "异常震动停层不稳", "type": "warning_signal"}, {"id": "trigger_025", "label": "限位系统失效", "type": "trigger_cause"}, {"id": "damage_025", "label": "升降机坠落", "type": "damage"}, {"id": "case_026", "label": "北京西城区\"9·5\"模板支撑体系坍塌事故调查报告", "type": "case", "case_id": "A026", "region": "北京市西城区", "time": "2022-09-05"}, {"id": "hazard_026", "label": "模板支撑体系未按方案搭设", "type": "hazard_behavior"}, {"id": "warning_026", "label": "支架晃动模板下沉", "type": "warning_signal"}, {"id": "trigger_026", "label": "支模结构失稳", "type": "trigger_cause"}, {"id": "damage_026", "label": "整体坍塌人员坠落", "type": "damage"}, {"id": "case_027", "label": "浙江某建筑工地卸料平台坍塌事故调查报告", "type": "case", "case_id": "A027", "region": "浙江省杭州市", "time": "2023-10-12"}, {"id": "hazard_027", "label": "卸料平台未按规范设计", "type": "hazard_behavior"}, {"id": "warning_027", "label": "平台晃动异响", "type": "warning_signal"}, {"id": "trigger_027", "label": "超载锚固失效", "type": "trigger_cause"}, {"id": "damage_027", "label": "平台坍塌作业人员坠落", "type": "damage"}, {"id": "case_028", "label": "深圳腾讯数码大厦\"6·15\"高处坠落事故调查报告", "type": "case", "case_id": "A028", "region": "广东省深圳市南山区", "time": "2022-06-15"}, {"id": "hazard_028", "label": "吊篮钢丝绳未检修", "type": "hazard_behavior"}, {"id": "warning_028", "label": "吊篮运行不稳异响", "type": "warning_signal"}, {"id": "trigger_028", "label": "钢丝绳断裂", "type": "trigger_cause"}, {"id": "damage_028", "label": "高空坠落死亡", "type": "damage"}, {"id": "case_029", "label": "南京市雨花台区住宅楼火灾事故调查报告", "type": "case", "case_id": "A029", "region": "江苏省南京市雨花台区", "time": "2024-02-23"}, {"id": "hazard_029", "label": "电动车违规充电", "type": "hazard_behavior"}, {"id": "warning_029", "label": "充电刺鼻气味电缆打火", "type": "warning_signal"}, {"id": "trigger_029", "label": "电动车充电起火", "type": "trigger_cause"}, {"id": "damage_029", "label": "火势蔓延烟气致伤亡", "type": "damage"}, {"id": "case_030", "label": "秦都区古渡新家园项目\"4·8\"电梯基坑挡土墙坍塌事故调查报告", "type": "case", "case_id": "A030", "region": "陕西省咸阳市秦都区", "time": "2023-04-08"}, {"id": "hazard_030", "label": "基坑支护设计不合理", "type": "hazard_behavior"}, {"id": "warning_030", "label": "墙体裂缝位移异响", "type": "warning_signal"}, {"id": "trigger_030", "label": "挡土墙结构失稳", "type": "trigger_cause"}, {"id": "damage_030", "label": "坍塌人员被埋", "type": "damage"}, {"id": "case_074", "label": "福建省泉州市欣佳酒店\"3·7\"坍塌事故", "type": "case", "case_id": "A074", "region": "福建省泉州市", "time": "2020-03-07"}, {"id": "hazard_074", "label": "违法加建违规加固", "type": "hazard_behavior"}, {"id": "warning_074", "label": "墙裂柱沉建筑晃动", "type": "warning_signal"}, {"id": "trigger_074", "label": "结构承载力不足", "type": "trigger_cause"}, {"id": "damage_074", "label": "建筑整体坍塌", "type": "damage"}, {"id": "case_078", "label": "莲花河畔景苑房屋倒塌事故", "type": "case", "case_id": "A078", "region": "上海市闵行区", "time": "2009-06-27"}, {"id": "hazard_078", "label": "北侧堆土过高南侧基坑开挖", "type": "hazard_behavior"}, {"id": "warning_078", "label": "土体水平位移风险", "type": "warning_signal"}, {"id": "trigger_078", "label": "桩基抗侧能力不足", "type": "trigger_cause"}, {"id": "damage_078", "label": "整栋楼房倒塌", "type": "damage"}, {"id": "case_080", "label": "郑州市地铁5号线\"7·20\"水浸事故", "type": "case", "case_id": "A080", "region": "河南省郑州市", "time": "2021-07-20"}, {"id": "hazard_080", "label": "排水系统设计不合理", "type": "hazard_behavior"}, {"id": "warning_080", "label": "排水能力不足隐患", "type": "warning_signal"}, {"id": "trigger_080", "label": "极端暴雨排水失效", "type": "trigger_cause"}, {"id": "damage_080", "label": "地下车库进水", "type": "damage"}, {"id": "case_087", "label": "安阳市厂房火灾事故", "type": "case", "case_id": "A087", "region": "河南省安阳市", "time": "2022-11-21"}, {"id": "hazard_087", "label": "违规电焊作业", "type": "hazard_behavior"}, {"id": "warning_087", "label": "棉絮飘落未清理", "type": "warning_signal"}, {"id": "trigger_087", "label": "电焊火花引燃", "type": "trigger_cause"}, {"id": "damage_087", "label": "厂房被烧毁", "type": "damage"}, {"id": "case_064", "label": "江苏响水县天嘉宜化工有限公司\"3·21\"特别重大爆炸事故", "type": "case", "case_id": "A064", "region": "江苏省盐城市", "time": "2019-03-21"}, {"id": "hazard_064", "label": "危险化学品贮存不规范", "type": "hazard_behavior"}, {"id": "warning_064", "label": "硝化废料积热升温", "type": "warning_signal"}, {"id": "trigger_064", "label": "硝化废料自燃爆炸", "type": "trigger_cause"}, {"id": "damage_064", "label": "厂区建筑坍塌", "type": "damage"}, {"id": "case_054", "label": "河南大学明伦校区大礼堂火灾事故", "type": "case", "case_id": "A054", "region": "河南省开封市", "time": "2024-05-02"}, {"id": "hazard_054", "label": "违规使用明火作业", "type": "hazard_behavior"}, {"id": "warning_054", "label": "木质结构存在火灾风险", "type": "warning_signal"}, {"id": "trigger_054", "label": "明火引燃木质屋面", "type": "trigger_cause"}, {"id": "damage_054", "label": "大礼堂屋顶坍塌", "type": "damage"}, {"id": "case_053", "label": "杭州地铁4号线\"7·8\"基坑土体突涌事故", "type": "case", "case_id": "A053", "region": "浙江省杭州市", "time": "2016-07-08"}, {"id": "hazard_053", "label": "基坑支护措施不足", "type": "hazard_behavior"}, {"id": "warning_053", "label": "基坑周边裂缝土体松动", "type": "warning_signal"}, {"id": "trigger_053", "label": "土体突涌", "type": "trigger_cause"}, {"id": "damage_053", "label": "施工人员被困", "type": "damage"}, {"id": "case_076", "label": "北角地盘升降机坠下事故", "type": "case", "case_id": "A076", "region": "香港特别行政区", "time": "1993-06-02"}, {"id": "hazard_076", "label": "升降机未定期检查", "type": "hazard_behavior"}, {"id": "warning_076", "label": "齿轮磨损超载运行", "type": "warning_signal"}, {"id": "trigger_076", "label": "升降机坠落", "type": "trigger_cause"}, {"id": "damage_076", "label": "升降机坠落事故", "type": "damage"}, {"id": "case_077", "label": "上海地铁4号线董家渡段建设事故", "type": "case", "case_id": "A077", "region": "上海市", "time": "2003-07-01"}, {"id": "hazard_077", "label": "隧道施工未有效控制渗水", "type": "hazard_behavior"}, {"id": "warning_077", "label": "隧道出现渗水现象", "type": "warning_signal"}, {"id": "trigger_077", "label": "流沙涌入隧道塌陷", "type": "trigger_cause"}, {"id": "damage_077", "label": "地面建筑部分坍塌", "type": "damage"}, {"id": "case_031", "label": "天津市红桥区凯莱赛商厦装修施工高处坠落事故调查报告", "type": "case", "case_id": "A031", "region": "天津市红桥区", "time": "2023-06-11"}, {"id": "hazard_031", "label": "脚手架搭设不规范", "type": "hazard_behavior"}, {"id": "warning_031", "label": "临边无防护", "type": "warning_signal"}, {"id": "trigger_031", "label": "失足坠落", "type": "trigger_cause"}, {"id": "damage_031", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_032", "label": "宁夏第二建筑有限公司\"10·30\"高处坠落事故调查报告", "type": "case", "case_id": "A032", "region": "宁夏回族自治区银川市兴庆区", "time": "2023-10-30"}, {"id": "hazard_032", "label": "脚手架无连续防护栏杆", "type": "hazard_behavior"}, {"id": "warning_032", "label": "脚手架边缘无护栏", "type": "warning_signal"}, {"id": "trigger_032", "label": "失足坠落", "type": "trigger_cause"}, {"id": "damage_032", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_038", "label": "深圳大疆天空之城大厦高处坠落事故调查报告", "type": "case", "case_id": "A038", "region": "广东省深圳市南山区", "time": "2023-05-22"}, {"id": "hazard_038", "label": "外墙作业未设固定锚点", "type": "hazard_behavior"}, {"id": "warning_038", "label": "高空挂点难固定", "type": "warning_signal"}, {"id": "trigger_038", "label": "安全带脱落坠落", "type": "trigger_cause"}, {"id": "damage_038", "label": "高空坠落死亡", "type": "damage"}, {"id": "case_042", "label": "铜梁区巴中利伟建设工程有限公司\"8·7\"坍塌事故调查报告", "type": "case", "case_id": "A042", "region": "重庆市铜梁区", "time": "2023-08-07"}, {"id": "hazard_042", "label": "脚手架搭设未按方案实施", "type": "hazard_behavior"}, {"id": "warning_042", "label": "脚手架局部晃动", "type": "warning_signal"}, {"id": "trigger_042", "label": "脚手架整体坍塌", "type": "trigger_cause"}, {"id": "damage_042", "label": "砸中作业人员", "type": "damage"}, {"id": "case_044", "label": "重庆龙益建筑工程有限公司\"9·8\"高处坠落事故调查报告", "type": "case", "case_id": "A044", "region": "重庆市大足区", "time": "2023-09-08"}, {"id": "hazard_044", "label": "楼面临边未设防护栏杆", "type": "hazard_behavior"}, {"id": "warning_044", "label": "临边区域长期敞开", "type": "warning_signal"}, {"id": "trigger_044", "label": "失足坠落地面", "type": "trigger_cause"}, {"id": "damage_044", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_045", "label": "航天建筑设计研究院有限公司\"8·23\"物体打击事故调查报告", "type": "case", "case_id": "A045", "region": "北京市昌平区", "time": "2023-08-23"}, {"id": "hazard_045", "label": "吊装作业未执行双钩挂牢", "type": "hazard_behavior"}, {"id": "warning_045", "label": "吊运模板晃动", "type": "warning_signal"}, {"id": "trigger_045", "label": "模板脱钩坠落", "type": "trigger_cause"}, {"id": "damage_045", "label": "砸中下方人员", "type": "damage"}, {"id": "case_046", "label": "湖北佳丽建筑工程有限公司\"10·16\"高处坠落事故调查报告", "type": "case", "case_id": "A046", "region": "湖北省黄冈市黄州区", "time": "2023-10-16"}, {"id": "hazard_046", "label": "脚手架搭设不规范", "type": "hazard_behavior"}, {"id": "warning_046", "label": "脚手架踏板松动", "type": "warning_signal"}, {"id": "trigger_046", "label": "踩踏松动踏板坠落", "type": "trigger_cause"}, {"id": "damage_046", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_048", "label": "宁夏鹏聿机械设备租赁有限公司\"10·8\"机械伤害事故", "type": "case", "case_id": "A048", "region": "宁夏回族自治区银川市", "time": "2019-10-08"}, {"id": "hazard_048", "label": "设备未断电进行清理", "type": "hazard_behavior"}, {"id": "warning_048", "label": "设备未断电缺乏防护", "type": "warning_signal"}, {"id": "trigger_048", "label": "被搅拌机卷入", "type": "trigger_cause"}, {"id": "damage_048", "label": "机械伤害死亡", "type": "damage"}, {"id": "case_049", "label": "深圳市某复式房装修现场楼板坍塌事故", "type": "case", "case_id": "A049", "region": "广东省深圳市", "time": "2021-07-17"}, {"id": "hazard_049", "label": "未经评估擅自拆除墙体", "type": "hazard_behavior"}, {"id": "warning_049", "label": "拆除前未进行结构评估", "type": "warning_signal"}, {"id": "trigger_049", "label": "楼板失去支撑坍塌", "type": "trigger_cause"}, {"id": "damage_049", "label": "楼板坍塌人员受伤", "type": "damage"}, {"id": "case_050", "label": "广西南宁市某工地基坑边坡塌方事故调查报告", "type": "case", "case_id": "A050", "region": "广西壮族自治区南宁市", "time": "2023-06-18"}, {"id": "hazard_050", "label": "基坑支护不到位", "type": "hazard_behavior"}, {"id": "warning_050", "label": "边坡裂缝雨后渗漏", "type": "warning_signal"}, {"id": "trigger_050", "label": "边坡稳定性失控塌方", "type": "trigger_cause"}, {"id": "damage_050", "label": "人员被掩埋", "type": "damage"}, {"id": "case_058", "label": "河南省原阳县盛和府建筑工地\"4·18\"土方压埋事故", "type": "case", "case_id": "A058", "region": "河南省新乡市原阳县", "time": "2020-04-18"}, {"id": "hazard_058", "label": "施工现场安全管理缺失", "type": "hazard_behavior"}, {"id": "warning_058", "label": "儿童进入施工区域", "type": "warning_signal"}, {"id": "trigger_058", "label": "土方倾卸压埋", "type": "trigger_cause"}, {"id": "damage_058", "label": "儿童死亡", "type": "damage"}, {"id": "case_059", "label": "新乡市荣校路雨水连通工程\"5·28\"窨井中毒窒息事故", "type": "case", "case_id": "A059", "region": "河南省新乡市", "time": "2020-05-28"}, {"id": "hazard_059", "label": "窨井作业缺乏安全措施", "type": "hazard_behavior"}, {"id": "warning_059", "label": "有毒气体检测缺失", "type": "warning_signal"}, {"id": "trigger_059", "label": "中毒窒息", "type": "trigger_cause"}, {"id": "damage_059", "label": "人员中毒死亡", "type": "damage"}, {"id": "case_060", "label": "深圳大鹏海洋生物产业园\"10·8\"触电事故调查报告", "type": "case", "case_id": "A060", "region": "广东省深圳市大鹏新区", "time": "2023-10-08"}, {"id": "hazard_060", "label": "临时用电未断电作业", "type": "hazard_behavior"}, {"id": "warning_060", "label": "临时用电打火", "type": "warning_signal"}, {"id": "trigger_060", "label": "触碰带电设备", "type": "trigger_cause"}, {"id": "damage_060", "label": "触电死亡", "type": "damage"}, {"id": "case_061", "label": "广州逢源街\"9·7\"装修高处坠落事故", "type": "case", "case_id": "A061", "region": "广东省广州市荔湾区", "time": "2023-09-07"}, {"id": "hazard_061", "label": "高处作业未设安全防护", "type": "hazard_behavior"}, {"id": "warning_061", "label": "作业人员未佩戴防护装备", "type": "warning_signal"}, {"id": "trigger_061", "label": "失足坠落", "type": "trigger_cause"}, {"id": "damage_061", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_062", "label": "香港环球贸易广场\"9·13\"升降机槽坠落事故", "type": "case", "case_id": "A062", "region": "香港特别行政区", "time": "2009-09-13"}, {"id": "hazard_062", "label": "临时工作平台搭设不规范", "type": "hazard_behavior"}, {"id": "warning_062", "label": "平台承载力未评估", "type": "warning_signal"}, {"id": "trigger_062", "label": "平台坍塌坠落", "type": "trigger_cause"}, {"id": "damage_062", "label": "多人坠落死亡", "type": "damage"}, {"id": "case_063", "label": "贵州仁怀市公租房建设项目\"3·15\"坍塌事故", "type": "case", "case_id": "A063", "region": "贵州省遵义市仁怀市", "time": "2021-03-15"}, {"id": "hazard_063", "label": "模板支撑体系未加固", "type": "hazard_behavior"}, {"id": "warning_063", "label": "混凝土浇筑高度过高", "type": "warning_signal"}, {"id": "trigger_063", "label": "支撑体系失稳坍塌", "type": "trigger_cause"}, {"id": "damage_063", "label": "建筑结构坍塌", "type": "damage"}, {"id": "case_065", "label": "上海市浦东新区\"5·10\"幕墙玻璃坠落事故", "type": "case", "case_id": "A065", "region": "上海市浦东新区", "time": "2022-05-10"}, {"id": "hazard_065", "label": "幕墙玻璃安装不规范", "type": "hazard_behavior"}, {"id": "warning_065", "label": "未定期检查维护", "type": "warning_signal"}, {"id": "trigger_065", "label": "强风天气玻璃脱落", "type": "trigger_cause"}, {"id": "damage_065", "label": "玻璃坠落伤人", "type": "damage"}, {"id": "case_033", "label": "河南省金虎建筑劳务有限公司\"5·03\"隧道施工事故", "type": "case", "case_id": "A033", "region": "河南省", "time": "2023-05-03"}, {"id": "hazard_033", "label": "隧道支护措施不足", "type": "hazard_behavior"}, {"id": "warning_033", "label": "围岩变形征象", "type": "warning_signal"}, {"id": "trigger_033", "label": "隧道支护结构失稳", "type": "trigger_cause"}, {"id": "damage_033", "label": "隧道坍塌作业人员被埋", "type": "damage"}, {"id": "case_034", "label": "广州地铁21号线盾构隧道塌方事故", "type": "case", "case_id": "A034", "region": "广东省广州市", "time": "2019-06-15"}, {"id": "hazard_034", "label": "盾构机换刀作业操作不当", "type": "hazard_behavior"}, {"id": "warning_034", "label": "隧道结构异常", "type": "warning_signal"}, {"id": "trigger_034", "label": "隧道结构失稳塌方", "type": "trigger_cause"}, {"id": "damage_034", "label": "隧道塌方", "type": "damage"}, {"id": "case_035", "label": "黄圃高架工程平台滑落事故", "type": "case", "case_id": "A035", "region": "广东省中山市", "time": "2019-06-10"}, {"id": "hazard_035", "label": "作业平台搭设不规范", "type": "hazard_behavior"}, {"id": "warning_035", "label": "支撑结构强度不足", "type": "warning_signal"}, {"id": "trigger_035", "label": "平台滑落", "type": "trigger_cause"}, {"id": "damage_035", "label": "作业人员坠落", "type": "damage"}, {"id": "case_036", "label": "海口市东湖天桥旁建筑工地\"12·30\"坍塌事故调查报告", "type": "case", "case_id": "A036", "region": "海南省海口市", "time": "2022-12-30"}, {"id": "hazard_036", "label": "基坑开挖深度超标", "type": "hazard_behavior"}, {"id": "warning_036", "label": "鼓胀冒水塌边前兆", "type": "warning_signal"}, {"id": "trigger_036", "label": "基坑支护失效坍塌", "type": "trigger_cause"}, {"id": "damage_036", "label": "基坑坍塌", "type": "damage"}, {"id": "case_037", "label": "广东东莞天然气站地盘塌方事故", "type": "case", "case_id": "A037", "region": "广东省东莞市", "time": "2019-05-20"}, {"id": "hazard_037", "label": "基坑支护不到位", "type": "hazard_behavior"}, {"id": "warning_037", "label": "土方开挖深度超标", "type": "warning_signal"}, {"id": "trigger_037", "label": "地盘塌方", "type": "trigger_cause"}, {"id": "damage_037", "label": "作业人员被埋", "type": "damage"}, {"id": "case_039", "label": "百色市右江区民房火灾事故", "type": "case", "case_id": "A039", "region": "广西壮族自治区百色市右江区", "time": "2025-01-15"}, {"id": "hazard_039", "label": "电动自行车楼道违规充电", "type": "hazard_behavior"}, {"id": "warning_039", "label": "锂电池充电异常", "type": "warning_signal"}, {"id": "trigger_039", "label": "锂电池起火", "type": "trigger_cause"}, {"id": "damage_039", "label": "民房火灾人员伤亡", "type": "damage"}, {"id": "case_040", "label": "重庆市某工程\"6·2\"高处坠落事故", "type": "case", "case_id": "A040", "region": "重庆市", "time": "2025-06-02"}, {"id": "hazard_040", "label": "脚手架跳板未固定", "type": "hazard_behavior"}, {"id": "warning_040", "label": "作业人员未系安全带", "type": "warning_signal"}, {"id": "trigger_040", "label": "踩踏松动跳板坠落", "type": "trigger_cause"}, {"id": "damage_040", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_041", "label": "平罗县康湖水岸\"8·2\"高处坠落事故", "type": "case", "case_id": "A041", "region": "宁夏回族自治区石嘴山市平罗县", "time": "2025-08-21"}, {"id": "hazard_041", "label": "作业人员未佩戴安全带", "type": "hazard_behavior"}, {"id": "warning_041", "label": "擅自从室内钻出至外脚手架", "type": "warning_signal"}, {"id": "trigger_041", "label": "失足坠落", "type": "trigger_cause"}, {"id": "damage_041", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_043", "label": "平安区翰林华府工程高处坠落事故", "type": "case", "case_id": "A043", "region": "青海省海东市平安区", "time": "2023-07-15"}, {"id": "hazard_043", "label": "作业区域未设置防护栏杆", "type": "hazard_behavior"}, {"id": "warning_043", "label": "作业人员未佩戴安全带", "type": "warning_signal"}, {"id": "trigger_043", "label": "从高处坠落", "type": "trigger_cause"}, {"id": "damage_043", "label": "作业人员死亡", "type": "damage"}, {"id": "case_047", "label": "重庆市安平建筑有限责任公司\"8·20\"高处坠落事故", "type": "case", "case_id": "A047", "region": "重庆市", "time": "2025-08-20"}, {"id": "hazard_047", "label": "作业人员未正确使用安全带", "type": "hazard_behavior"}, {"id": "warning_047", "label": "作业区域未设置防护措施", "type": "warning_signal"}, {"id": "trigger_047", "label": "高处坠落", "type": "trigger_cause"}, {"id": "damage_047", "label": "作业人员死亡", "type": "damage"}, {"id": "case_051", "label": "南宁市某装修工地高处坠落事故调查报告", "type": "case", "case_id": "A051", "region": "广西壮族自治区南宁市", "time": "2023-07-10"}, {"id": "hazard_051", "label": "临边无护栏", "type": "hazard_behavior"}, {"id": "warning_051", "label": "作业人员未系安全带", "type": "warning_signal"}, {"id": "trigger_051", "label": "失足坠落", "type": "trigger_cause"}, {"id": "damage_051", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_052", "label": "瑞德青春（峨眉）健康管理项目建筑边坡防护工程事故", "type": "case", "case_id": "A052", "region": "四川省乐山市峨眉山市", "time": "2023-08-12"}, {"id": "hazard_052", "label": "边坡作业未设置防护措施", "type": "hazard_behavior"}, {"id": "warning_052", "label": "作业人员未佩戴安全装备", "type": "warning_signal"}, {"id": "trigger_052", "label": "边坡滑坡", "type": "trigger_cause"}, {"id": "damage_052", "label": "作业人员受伤", "type": "damage"}, {"id": "case_055", "label": "宜昌市某建筑工地电梯井坠落事故调查报告", "type": "case", "case_id": "A055", "region": "湖北省宜昌市", "time": "2023-06-08"}, {"id": "hazard_055", "label": "电梯井未设防护栏", "type": "hazard_behavior"}, {"id": "warning_055", "label": "作业人员未系安全带", "type": "warning_signal"}, {"id": "trigger_055", "label": "坠入电梯井", "type": "trigger_cause"}, {"id": "damage_055", "label": "电梯井坠落死亡", "type": "damage"}, {"id": "case_056", "label": "天津华夏未来装饰公司电梯井道坠落事故调查报告", "type": "case", "case_id": "A056", "region": "天津市", "time": "2022-12-08"}, {"id": "hazard_056", "label": "井口无防护", "type": "hazard_behavior"}, {"id": "warning_056", "label": "材料遮挡视线", "type": "warning_signal"}, {"id": "trigger_056", "label": "误入井道坠落", "type": "trigger_cause"}, {"id": "damage_056", "label": "电梯井道坠落死亡", "type": "damage"}, {"id": "case_057", "label": "工厂屋顶维修工人坠落事故", "type": "case", "case_id": "A057", "region": "马来西亚", "time": "2025-03-14"}, {"id": "hazard_057", "label": "屋顶维修未评估结构安全性", "type": "hazard_behavior"}, {"id": "warning_057", "label": "未采取防坠落措施", "type": "warning_signal"}, {"id": "trigger_057", "label": "屋顶坍塌坠落", "type": "trigger_cause"}, {"id": "damage_057", "label": "工人坠落死亡", "type": "damage"}, {"id": "case_066", "label": "北京市丰台区\"1·17\"临边作业坠落事故", "type": "case", "case_id": "A066", "region": "北京市丰台区", "time": "2023-01-17"}, {"id": "hazard_066", "label": "高处作业未佩戴安全带", "type": "hazard_behavior"}, {"id": "warning_066", "label": "缺乏安全防护措施", "type": "warning_signal"}, {"id": "trigger_066", "label": "临边作业坠落", "type": "trigger_cause"}, {"id": "damage_066", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_067", "label": "北京市昌平区实验中学施工破坏燃气管线事故", "type": "case", "case_id": "A067", "region": "北京市昌平区", "time": "2023-07-10"}, {"id": "hazard_067", "label": "施工未履行安全生产管理责任", "type": "hazard_behavior"}, {"id": "warning_067", "label": "违反相关法规", "type": "warning_signal"}, {"id": "trigger_067", "label": "燃气管线破坏", "type": "trigger_cause"}, {"id": "damage_067", "label": "燃气泄漏安全隐患", "type": "damage"}, {"id": "case_068", "label": "上海南京东路华联商厦电梯井坠落事故调查报告", "type": "case", "case_id": "A068", "region": "上海市黄浦区", "time": "2022-07-03"}, {"id": "hazard_068", "label": "电梯井口无防护无标识", "type": "hazard_behavior"}, {"id": "warning_068", "label": "作业无监护", "type": "warning_signal"}, {"id": "trigger_068", "label": "误入井道坠落", "type": "trigger_cause"}, {"id": "damage_068", "label": "电梯井坠落死亡", "type": "damage"}, {"id": "case_069", "label": "广州珠江隧道地面塌陷事故", "type": "case", "case_id": "A069", "region": "广东省广州市", "time": "2021-12-01"}, {"id": "hazard_069", "label": "施工未有效控制地下水", "type": "hazard_behavior"}, {"id": "warning_069", "label": "地下水位异常", "type": "warning_signal"}, {"id": "trigger_069", "label": "地面塌陷", "type": "trigger_cause"}, {"id": "damage_069", "label": "交通中断建筑受影响", "type": "damage"}, {"id": "case_070", "label": "鄂尔多斯市地下管道火灾事故", "type": "case", "case_id": "A070", "region": "内蒙古自治区鄂尔多斯市", "time": "2022-08-15"}, {"id": "hazard_070", "label": "地下管道维护不当", "type": "hazard_behavior"}, {"id": "warning_070", "label": "存在火灾隐患未及时处理", "type": "warning_signal"}, {"id": "trigger_070", "label": "地下管道火灾", "type": "trigger_cause"}, {"id": "damage_070", "label": "人员疏散交通中断", "type": "damage"}, {"id": "case_071", "label": "武汉市三阳路幕墙作业吊篮坠落事故", "type": "case", "case_id": "A071", "region": "湖北省武汉市", "time": "2023-04-20"}, {"id": "hazard_071", "label": "强风天气仍进行高空作业", "type": "hazard_behavior"}, {"id": "warning_071", "label": "未及时关注天气变化", "type": "warning_signal"}, {"id": "trigger_071", "label": "吊篮坠落", "type": "trigger_cause"}, {"id": "damage_071", "label": "作业人员坠落伤亡", "type": "damage"}, {"id": "case_072", "label": "北京西城区\"9·5\"模板支撑体系坍塌事故", "type": "case", "case_id": "A072", "region": "北京市西城区", "time": "2016-09-05"}, {"id": "hazard_072", "label": "模板支撑体系设计不合理", "type": "hazard_behavior"}, {"id": "warning_072", "label": "施工未严格按方案执行", "type": "warning_signal"}, {"id": "trigger_072", "label": "支撑体系坍塌", "type": "trigger_cause"}, {"id": "damage_072", "label": "模板支撑坍塌", "type": "damage"}, {"id": "case_073", "label": "江西省某建筑施工水池墙体坍塌事故", "type": "case", "case_id": "A073", "region": "江西省", "time": "2024-02-25"}, {"id": "hazard_073", "label": "土方回填与墙面粉刷作业未协调", "type": "hazard_behavior"}, {"id": "warning_073", "label": "缺乏有效沟通与安全措施", "type": "warning_signal"}, {"id": "trigger_073", "label": "墙体受力不均坍塌", "type": "trigger_cause"}, {"id": "damage_073", "label": "水池墙体坍塌", "type": "damage"}, {"id": "case_075", "label": "南京江宁区基坑塌陷事故", "type": "case", "case_id": "A075", "region": "江苏省南京市江宁区", "time": "2018-01-13"}, {"id": "hazard_075", "label": "基坑支护结构存在隐患", "type": "hazard_behavior"}, {"id": "warning_075", "label": "未及时进行加固处理", "type": "warning_signal"}, {"id": "trigger_075", "label": "基坑塌陷", "type": "trigger_cause"}, {"id": "damage_075", "label": "基坑塌陷事故", "type": "damage"}, {"id": "case_079", "label": "北京市大兴区\"3·15\"高处坠落事故", "type": "case", "case_id": "A079", "region": "北京市大兴区", "time": "2023-03-15"}, {"id": "hazard_079", "label": "高处作业未佩戴安全带", "type": "hazard_behavior"}, {"id": "warning_079", "label": "安全防护措施缺失", "type": "warning_signal"}, {"id": "trigger_079", "label": "高处坠落", "type": "trigger_cause"}, {"id": "damage_079", "label": "作业人员坠落死亡", "type": "damage"}, {"id": "case_081", "label": "河南省金虎建筑劳务有限公司\"5.03\"事故调查报告", "type": "case", "case_id": "A081", "region": "河南省", "time": "2023-05-03"}, {"id": "hazard_081", "label": "隧道施工安全管理不到位", "type": "hazard_behavior"}, {"id": "warning_081", "label": "未严格执行安全操作规程", "type": "warning_signal"}, {"id": "trigger_081", "label": "掌子面围岩坍塌", "type": "trigger_cause"}, {"id": "damage_081", "label": "初期支护结构报废", "type": "damage"}, {"id": "case_082", "label": "倮果大桥桥面塌陷事故", "type": "case", "case_id": "A082", "region": "四川省攀枝花市", "time": "2019-08-10"}, {"id": "hazard_082", "label": "吊杆锚具系统老化", "type": "hazard_behavior"}, {"id": "warning_082", "label": "超载运行", "type": "warning_signal"}, {"id": "trigger_082", "label": "吊杆锚具失效横梁坠落", "type": "trigger_cause"}, {"id": "damage_082", "label": "桥面结构失稳塌陷", "type": "damage"}, {"id": "case_083", "label": "阳泉市城区燕竹花园四组团五期开发项目\"10·22\"一般物体打击事故调查报告", "type": "case", "case_id": "A083", "region": "山西省阳泉市城区", "time": "2023-10-22"}, {"id": "hazard_083", "label": "塔吊标准节未固定牢靠", "type": "hazard_behavior"}, {"id": "warning_083", "label": "人员在下方作业", "type": "warning_signal"}, {"id": "trigger_083", "label": "标准节坠落", "type": "trigger_cause"}, {"id": "damage_083", "label": "物体打击伤人", "type": "damage"}, {"id": "case_084", "label": "山东郓城县恒源锦绣城E区项目\"8·15\"高处作业吊篮倾覆事故调查报告", "type": "case", "case_id": "A084", "region": "山东省菏泽市郓城县", "time": "2023-08-15"}, {"id": "hazard_084", "label": "吊篮安装不规范", "type": "hazard_behavior"}, {"id": "warning_084", "label": "连接部位异常", "type": "warning_signal"}, {"id": "trigger_084", "label": "作业过程中倾覆", "type": "trigger_cause"}, {"id": "damage_084", "label": "吊篮倾覆作业中断", "type": "damage"}, {"id": "case_085", "label": "成都市武侯区某建筑工地\"7·05\"施工电梯坠落事故调查报告", "type": "case", "case_id": "A085", "region": "四川省成都市武侯区", "time": "2023-07-05"}, {"id": "hazard_085", "label": "电梯安全装置失效", "type": "hazard_behavior"}, {"id": "warning_085", "label": "未进行必要维护检查", "type": "warning_signal"}, {"id": "trigger_085", "label": "运行中坠落", "type": "trigger_cause"}, {"id": "damage_085", "label": "电梯坠落设备损毁", "type": "damage"}, {"id": "case_086", "label": "重庆市渝北区某建筑工地\"11·12\"起重伤害事故调查报告", "type": "case", "case_id": "A086", "region": "重庆市渝北区", "time": "2023-11-12"}, {"id": "hazard_086", "label": "吊运作业未设置安全警戒区域", "type": "hazard_behavior"}, {"id": "warning_086", "label": "作业人员在吊物下方作业", "type": "warning_signal"}, {"id": "trigger_086", "label": "钢筋滑落致人死亡", "type": "trigger_cause"}, {"id": "damage_086", "label": "起重伤害死亡", "type": "damage"}, {"id": "case_088", "label": "安徽铜陵楼房坍塌事故调查报告", "type": "case", "case_id": "A088", "region": "安徽省铜陵市", "time": "2022-07-15"}, {"id": "hazard_088", "label": "强降雨地基下沉", "type": "hazard_behavior"}, {"id": "warning_088", "label": "房屋裂缝", "type": "warning_signal"}, {"id": "trigger_088", "label": "结构失稳房屋坍塌", "type": "trigger_cause"}, {"id": "damage_088", "label": "建筑物部分坍塌", "type": "damage"}, {"id": "case_089", "label": "深圳前海民生银行互联网大厦\"7·8\"高坠事故调查报告", "type": "case", "case_id": "A089", "region": "广东省深圳市前海", "time": "2022-07-08"}, {"id": "hazard_089", "label": "无防护措施支护梁作业", "type": "hazard_behavior"}, {"id": "warning_089", "label": "缺乏个人防护装备", "type": "warning_signal"}, {"id": "trigger_089", "label": "失足坠落", "type": "trigger_cause"}, {"id": "damage_089", "label": "高空坠落死亡", "type": "damage"}, {"id": "case_090", "label": "上海卓观建筑装潢公司\"7·26\"物体打击事故调查报告", "type": "case", "case_id": "A090", "region": "上海市青浦区", "time": "2024-07-26"}, {"id": "hazard_090", "label": "拆除缺乏结构评估", "type": "hazard_behavior"}, {"id": "warning_090", "label": "缺乏支撑措施", "type": "warning_signal"}, {"id": "trigger_090", "label": "吊顶结构失稳坠落", "type": "trigger_cause"}, {"id": "damage_090", "label": "物体打击伤害", "type": "damage"}, {"id": "case_091", "label": "天津铁马建筑工程有限公司\"8·5\"起重伤害事故调查报告", "type": "case", "case_id": "A091", "region": "天津市滨海新区", "time": "2022-08-05"}, {"id": "hazard_091", "label": "无安全措施跳车", "type": "hazard_behavior"}, {"id": "warning_091", "label": "缺乏安全操作规程和培训", "type": "warning_signal"}, {"id": "trigger_091", "label": "跳车头部受伤", "type": "trigger_cause"}, {"id": "damage_091", "label": "起重伤害死亡", "type": "damage"}, {"id": "case_092", "label": "黄圃高架工程平台滑落事故调查报告", "type": "case", "case_id": "A092", "region": "广东省中山市", "time": "2019-06-12"}, {"id": "hazard_092", "label": "雨天柱子湿滑平台固定不牢", "type": "hazard_behavior"}, {"id": "warning_092", "label": "平台倾斜和晃动", "type": "warning_signal"}, {"id": "trigger_092", "label": "平台滑落", "type": "trigger_cause"}, {"id": "damage_092", "label": "工人受伤设备损坏", "type": "damage"}, {"id": "case_093", "label": "海口东湖天桥旁工地坍塌事故调查报告", "type": "case", "case_id": "A093", "region": "海南省海口市", "time": "2019-06-15"}, {"id": "hazard_093", "label": "土方开挖过深支护不到位", "type": "hazard_behavior"}, {"id": "warning_093", "label": "土体裂缝和下沉", "type": "warning_signal"}, {"id": "trigger_093", "label": "塌方事故", "type": "trigger_cause"}, {"id": "damage_093", "label": "工地部分结构受损", "type": "damage"}, {"id": "case_094", "label": "东莞天然气站地盘塌方事故调查报告", "type": "case", "case_id": "A094", "region": "广东省东莞市", "time": "2019-06-18"}, {"id": "hazard_094", "label": "地质结构差支护不到位", "type": "hazard_behavior"}, {"id": "warning_094", "label": "土壁渗水和裂缝", "type": "warning_signal"}, {"id": "trigger_094", "label": "地盘塌方", "type": "trigger_cause"}, {"id": "damage_094", "label": "工人受伤安全隐患增加", "type": "damage"}, {"id": "case_095", "label": "青浦徐泾上海卓观建筑装潢设计工程有限公司\"7·26\"物体打击事故调查报告", "type": "case", "case_id": "A095", "region": "上海市青浦区", "time": "2024-07-26"}, {"id": "hazard_095", "label": "拆除作业缺乏结构评估", "type": "hazard_behavior"}, {"id": "warning_095", "label": "缺乏支撑措施", "type": "warning_signal"}, {"id": "trigger_095", "label": "结构失稳坠落", "type": "trigger_cause"}, {"id": "damage_095", "label": "物体打击事故", "type": "damage"}, {"id": "case_096", "label": "江西省东亚景观设计工程有限公司\"7·13\"一般物体打击事故调查报告", "type": "case", "case_id": "A096", "region": "江西省南昌市新建区", "time": "2024-07-13"}, {"id": "hazard_096", "label": "作业人员违规操作", "type": "hazard_behavior"}, {"id": "warning_096", "label": "现场管理混乱", "type": "warning_signal"}, {"id": "trigger_096", "label": "吊装物体坠落", "type": "trigger_cause"}, {"id": "damage_096", "label": "物体打击伤害", "type": "damage"}, {"id": "case_097", "label": "青浦金泽上海锴倡建筑工程有限公司\"12·23\"高处坠落事故调查报告", "type": "case", "case_id": "A097", "region": "上海市青浦区", "time": "2023-12-23"}, {"id": "hazard_097", "label": "缺乏高处作业防护措施", "type": "hazard_behavior"}, {"id": "warning_097", "label": "安全管理不到位", "type": "warning_signal"}, {"id": "trigger_097", "label": "高处坠落", "type": "trigger_cause"}, {"id": "damage_097", "label": "作业人员死亡", "type": "damage"}, {"id": "case_098", "label": "中建三局第一建设工程有限责任公司\"3·15\"高处坠落事故调查报告", "type": "case", "case_id": "A098", "region": "北京市大兴区", "time": "2023-03-15"}, {"id": "hazard_098", "label": "缺乏高处作业防护措施", "type": "hazard_behavior"}, {"id": "warning_098", "label": "安全管理严重不足", "type": "warning_signal"}, {"id": "trigger_098", "label": "高处坠落", "type": "trigger_cause"}, {"id": "damage_098", "label": "作业人员死亡", "type": "damage"}, {"id": "case_099", "label": "台山市南区\"3·12\"高处坠落事故调查报告", "type": "case", "case_id": "A099", "region": "广东省江门市台山市", "time": "2023-03-12"}, {"id": "hazard_099", "label": "无防护措施高处作业", "type": "hazard_behavior"}, {"id": "warning_099", "label": "缺乏安全管理", "type": "warning_signal"}, {"id": "trigger_099", "label": "作业人员坠落", "type": "trigger_cause"}, {"id": "damage_099", "label": "高处坠落死亡", "type": "damage"}, {"id": "case_100", "label": "沈阳化工建设工程总公司\"4·6\"高处坠落事故调查报告", "type": "case", "case_id": "A100", "region": "辽宁省沈阳市", "time": "2023-04-06"}, {"id": "hazard_100", "label": "高处作业安全措施不到位", "type": "hazard_behavior"}, {"id": "warning_100", "label": "作业人员未佩戴防护装备", "type": "warning_signal"}, {"id": "trigger_100", "label": "高处坠落", "type": "trigger_cause"}, {"id": "damage_100", "label": "作业人员死亡", "type": "damage"}], "relationships": [{"source": "case_001", "target": "hazard_001", "relation": "involves", "case_id": "A001"}, {"source": "hazard_001", "target": "warning_001", "relation": "precedes", "case_id": "A001"}, {"source": "warning_001", "target": "trigger_001", "relation": "leads_to", "case_id": "A001"}, {"source": "trigger_001", "target": "damage_001", "relation": "causes", "case_id": "A001"}, {"source": "case_002", "target": "hazard_002", "relation": "involves", "case_id": "A002"}, {"source": "hazard_002", "target": "warning_002", "relation": "precedes", "case_id": "A002"}, {"source": "warning_002", "target": "trigger_002", "relation": "leads_to", "case_id": "A002"}, {"source": "trigger_002", "target": "damage_001", "relation": "causes", "case_id": "A002"}, {"source": "case_003", "target": "hazard_003", "relation": "involves", "case_id": "A003"}, {"source": "hazard_003", "target": "warning_003", "relation": "precedes", "case_id": "A003"}, {"source": "warning_003", "target": "trigger_003", "relation": "leads_to", "case_id": "A003"}, {"source": "trigger_003", "target": "damage_003", "relation": "causes", "case_id": "A003"}, {"source": "case_004", "target": "hazard_004", "relation": "involves", "case_id": "A004"}, {"source": "hazard_004", "target": "warning_004", "relation": "precedes", "case_id": "A004"}, {"source": "warning_004", "target": "trigger_004", "relation": "leads_to", "case_id": "A004"}, {"source": "trigger_004", "target": "damage_004", "relation": "causes", "case_id": "A004"}, {"source": "case_005", "target": "hazard_005", "relation": "involves", "case_id": "A005"}, {"source": "hazard_005", "target": "warning_005", "relation": "precedes", "case_id": "A005"}, {"source": "warning_005", "target": "trigger_005", "relation": "leads_to", "case_id": "A005"}, {"source": "trigger_005", "target": "damage_005", "relation": "causes", "case_id": "A005"}, {"source": "case_006", "target": "hazard_006", "relation": "involves", "case_id": "A006"}, {"source": "hazard_006", "target": "warning_006", "relation": "precedes", "case_id": "A006"}, {"source": "warning_006", "target": "trigger_006", "relation": "leads_to", "case_id": "A006"}, {"source": "trigger_006", "target": "damage_006", "relation": "causes", "case_id": "A006"}, {"source": "case_007", "target": "hazard_007", "relation": "involves", "case_id": "A007"}, {"source": "hazard_007", "target": "warning_007", "relation": "precedes", "case_id": "A007"}, {"source": "warning_007", "target": "trigger_007", "relation": "leads_to", "case_id": "A007"}, {"source": "trigger_007", "target": "damage_006", "relation": "causes", "case_id": "A007"}, {"source": "case_008", "target": "hazard_008", "relation": "involves", "case_id": "A008"}, {"source": "hazard_008", "target": "warning_008", "relation": "precedes", "case_id": "A008"}, {"source": "warning_008", "target": "trigger_008", "relation": "leads_to", "case_id": "A008"}, {"source": "trigger_008", "target": "damage_006", "relation": "causes", "case_id": "A008"}, {"source": "case_009", "target": "hazard_009", "relation": "involves", "case_id": "A009"}, {"source": "hazard_009", "target": "warning_009", "relation": "precedes", "case_id": "A009"}, {"source": "warning_009", "target": "trigger_009", "relation": "leads_to", "case_id": "A009"}, {"source": "trigger_009", "target": "damage_005", "relation": "causes", "case_id": "A009"}, {"source": "case_010", "target": "hazard_010", "relation": "involves", "case_id": "A010"}, {"source": "hazard_010", "target": "warning_010", "relation": "precedes", "case_id": "A010"}, {"source": "warning_010", "target": "trigger_010", "relation": "leads_to", "case_id": "A010"}, {"source": "trigger_010", "target": "damage_010", "relation": "causes", "case_id": "A010"}, {"source": "case_011", "target": "hazard_011", "relation": "involves", "case_id": "A011"}, {"source": "hazard_011", "target": "warning_011", "relation": "precedes", "case_id": "A011"}, {"source": "warning_011", "target": "trigger_011", "relation": "leads_to", "case_id": "A011"}, {"source": "trigger_011", "target": "damage_011", "relation": "causes", "case_id": "A011"}, {"source": "case_012", "target": "hazard_012", "relation": "involves", "case_id": "A012"}, {"source": "hazard_012", "target": "warning_012", "relation": "precedes", "case_id": "A012"}, {"source": "warning_012", "target": "trigger_012", "relation": "leads_to", "case_id": "A012"}, {"source": "trigger_012", "target": "damage_012", "relation": "causes", "case_id": "A012"}, {"source": "case_013", "target": "hazard_013", "relation": "involves", "case_id": "A013"}, {"source": "hazard_013", "target": "warning_013", "relation": "precedes", "case_id": "A013"}, {"source": "warning_013", "target": "trigger_013", "relation": "leads_to", "case_id": "A013"}, {"source": "trigger_013", "target": "damage_013", "relation": "causes", "case_id": "A013"}, {"source": "case_014", "target": "hazard_014", "relation": "involves", "case_id": "A014"}, {"source": "hazard_014", "target": "warning_014", "relation": "precedes", "case_id": "A014"}, {"source": "warning_014", "target": "trigger_014", "relation": "leads_to", "case_id": "A014"}, {"source": "trigger_014", "target": "damage_014", "relation": "causes", "case_id": "A014"}, {"source": "case_015", "target": "hazard_015", "relation": "involves", "case_id": "A015"}, {"source": "hazard_015", "target": "warning_015", "relation": "precedes", "case_id": "A015"}, {"source": "warning_015", "target": "trigger_015", "relation": "leads_to", "case_id": "A015"}, {"source": "trigger_015", "target": "damage_015", "relation": "causes", "case_id": "A015"}, {"source": "case_016", "target": "hazard_016", "relation": "involves", "case_id": "A016"}, {"source": "hazard_016", "target": "warning_016", "relation": "precedes", "case_id": "A016"}, {"source": "warning_016", "target": "trigger_016", "relation": "leads_to", "case_id": "A016"}, {"source": "trigger_016", "target": "damage_016", "relation": "causes", "case_id": "A016"}, {"source": "case_017", "target": "hazard_017", "relation": "involves", "case_id": "A017"}, {"source": "hazard_017", "target": "warning_017", "relation": "precedes", "case_id": "A017"}, {"source": "warning_017", "target": "trigger_017", "relation": "leads_to", "case_id": "A017"}, {"source": "trigger_017", "target": "damage_017", "relation": "causes", "case_id": "A017"}, {"source": "case_018", "target": "hazard_018", "relation": "involves", "case_id": "A018"}, {"source": "hazard_018", "target": "warning_018", "relation": "precedes", "case_id": "A018"}, {"source": "warning_018", "target": "trigger_018", "relation": "leads_to", "case_id": "A018"}, {"source": "trigger_018", "target": "damage_018", "relation": "causes", "case_id": "A018"}, {"source": "case_019", "target": "hazard_019", "relation": "involves", "case_id": "A019"}, {"source": "hazard_019", "target": "warning_019", "relation": "precedes", "case_id": "A019"}, {"source": "warning_019", "target": "trigger_019", "relation": "leads_to", "case_id": "A019"}, {"source": "trigger_019", "target": "damage_019", "relation": "causes", "case_id": "A019"}, {"source": "case_020", "target": "hazard_020", "relation": "involves", "case_id": "A020"}, {"source": "hazard_020", "target": "warning_020", "relation": "precedes", "case_id": "A020"}, {"source": "warning_020", "target": "trigger_020", "relation": "leads_to", "case_id": "A020"}, {"source": "trigger_020", "target": "damage_020", "relation": "causes", "case_id": "A020"}, {"source": "case_074", "target": "hazard_074", "relation": "involves", "case_id": "A074"}, {"source": "hazard_074", "target": "warning_074", "relation": "precedes", "case_id": "A074"}, {"source": "warning_074", "target": "trigger_074", "relation": "leads_to", "case_id": "A074"}, {"source": "trigger_074", "target": "damage_074", "relation": "causes", "case_id": "A074"}, {"source": "case_078", "target": "hazard_078", "relation": "involves", "case_id": "A078"}, {"source": "hazard_078", "target": "warning_078", "relation": "precedes", "case_id": "A078"}, {"source": "warning_078", "target": "trigger_078", "relation": "leads_to", "case_id": "A078"}, {"source": "trigger_078", "target": "damage_078", "relation": "causes", "case_id": "A078"}, {"source": "case_080", "target": "hazard_080", "relation": "involves", "case_id": "A080"}, {"source": "hazard_080", "target": "warning_080", "relation": "precedes", "case_id": "A080"}, {"source": "warning_080", "target": "trigger_080", "relation": "leads_to", "case_id": "A080"}, {"source": "trigger_080", "target": "damage_080", "relation": "causes", "case_id": "A080"}, {"source": "case_087", "target": "hazard_087", "relation": "involves", "case_id": "A087"}, {"source": "hazard_087", "target": "warning_087", "relation": "precedes", "case_id": "A087"}, {"source": "warning_087", "target": "trigger_087", "relation": "leads_to", "case_id": "A087"}, {"source": "trigger_087", "target": "damage_087", "relation": "causes", "case_id": "A087"}, {"source": "case_064", "target": "hazard_064", "relation": "involves", "case_id": "A064"}, {"source": "hazard_064", "target": "warning_064", "relation": "precedes", "case_id": "A064"}, {"source": "warning_064", "target": "trigger_064", "relation": "leads_to", "case_id": "A064"}, {"source": "trigger_064", "target": "damage_064", "relation": "causes", "case_id": "A064"}, {"source": "case_054", "target": "hazard_054", "relation": "involves", "case_id": "A054"}, {"source": "hazard_054", "target": "warning_054", "relation": "precedes", "case_id": "A054"}, {"source": "warning_054", "target": "trigger_054", "relation": "leads_to", "case_id": "A054"}, {"source": "trigger_054", "target": "damage_054", "relation": "causes", "case_id": "A054"}, {"source": "case_053", "target": "hazard_053", "relation": "involves", "case_id": "A053"}, {"source": "hazard_053", "target": "warning_053", "relation": "precedes", "case_id": "A053"}, {"source": "warning_053", "target": "trigger_053", "relation": "leads_to", "case_id": "A053"}, {"source": "trigger_053", "target": "damage_053", "relation": "causes", "case_id": "A053"}, {"source": "case_076", "target": "hazard_076", "relation": "involves", "case_id": "A076"}, {"source": "hazard_076", "target": "warning_076", "relation": "precedes", "case_id": "A076"}, {"source": "warning_076", "target": "trigger_076", "relation": "leads_to", "case_id": "A076"}, {"source": "trigger_076", "target": "damage_076", "relation": "causes", "case_id": "A076"}, {"source": "case_077", "target": "hazard_077", "relation": "involves", "case_id": "A077"}, {"source": "hazard_077", "target": "warning_077", "relation": "precedes", "case_id": "A077"}, {"source": "warning_077", "target": "trigger_077", "relation": "leads_to", "case_id": "A077"}, {"source": "trigger_077", "target": "damage_077", "relation": "causes", "case_id": "A077"}, {"source": "case_031", "target": "hazard_031", "relation": "involves", "case_id": "A031"}, {"source": "hazard_031", "target": "warning_031", "relation": "precedes", "case_id": "A031"}, {"source": "warning_031", "target": "trigger_031", "relation": "leads_to", "case_id": "A031"}, {"source": "trigger_031", "target": "damage_031", "relation": "causes", "case_id": "A031"}, {"source": "case_032", "target": "hazard_032", "relation": "involves", "case_id": "A032"}, {"source": "hazard_032", "target": "warning_032", "relation": "precedes", "case_id": "A032"}, {"source": "warning_032", "target": "trigger_032", "relation": "leads_to", "case_id": "A032"}, {"source": "trigger_032", "target": "damage_032", "relation": "causes", "case_id": "A032"}, {"source": "case_038", "target": "hazard_038", "relation": "involves", "case_id": "A038"}, {"source": "hazard_038", "target": "warning_038", "relation": "precedes", "case_id": "A038"}, {"source": "warning_038", "target": "trigger_038", "relation": "leads_to", "case_id": "A038"}, {"source": "trigger_038", "target": "damage_038", "relation": "causes", "case_id": "A038"}, {"source": "case_042", "target": "hazard_042", "relation": "involves", "case_id": "A042"}, {"source": "hazard_042", "target": "warning_042", "relation": "precedes", "case_id": "A042"}, {"source": "warning_042", "target": "trigger_042", "relation": "leads_to", "case_id": "A042"}, {"source": "trigger_042", "target": "damage_042", "relation": "causes", "case_id": "A042"}, {"source": "case_044", "target": "hazard_044", "relation": "involves", "case_id": "A044"}, {"source": "hazard_044", "target": "warning_044", "relation": "precedes", "case_id": "A044"}, {"source": "warning_044", "target": "trigger_044", "relation": "leads_to", "case_id": "A044"}, {"source": "trigger_044", "target": "damage_044", "relation": "causes", "case_id": "A044"}, {"source": "case_045", "target": "hazard_045", "relation": "involves", "case_id": "A045"}, {"source": "hazard_045", "target": "warning_045", "relation": "precedes", "case_id": "A045"}, {"source": "warning_045", "target": "trigger_045", "relation": "leads_to", "case_id": "A045"}, {"source": "trigger_045", "target": "damage_045", "relation": "causes", "case_id": "A045"}, {"source": "case_046", "target": "hazard_046", "relation": "involves", "case_id": "A046"}, {"source": "hazard_046", "target": "warning_046", "relation": "precedes", "case_id": "A046"}, {"source": "warning_046", "target": "trigger_046", "relation": "leads_to", "case_id": "A046"}, {"source": "trigger_046", "target": "damage_046", "relation": "causes", "case_id": "A046"}, {"source": "case_048", "target": "hazard_048", "relation": "involves", "case_id": "A048"}, {"source": "hazard_048", "target": "warning_048", "relation": "precedes", "case_id": "A048"}, {"source": "warning_048", "target": "trigger_048", "relation": "leads_to", "case_id": "A048"}, {"source": "trigger_048", "target": "damage_048", "relation": "causes", "case_id": "A048"}, {"source": "case_049", "target": "hazard_049", "relation": "involves", "case_id": "A049"}, {"source": "hazard_049", "target": "warning_049", "relation": "precedes", "case_id": "A049"}, {"source": "warning_049", "target": "trigger_049", "relation": "leads_to", "case_id": "A049"}, {"source": "trigger_049", "target": "damage_049", "relation": "causes", "case_id": "A049"}, {"source": "case_050", "target": "hazard_050", "relation": "involves", "case_id": "A050"}, {"source": "hazard_050", "target": "warning_050", "relation": "precedes", "case_id": "A050"}, {"source": "warning_050", "target": "trigger_050", "relation": "leads_to", "case_id": "A050"}, {"source": "trigger_050", "target": "damage_050", "relation": "causes", "case_id": "A050"}, {"source": "case_058", "target": "hazard_058", "relation": "involves", "case_id": "A058"}, {"source": "hazard_058", "target": "warning_058", "relation": "precedes", "case_id": "A058"}, {"source": "warning_058", "target": "trigger_058", "relation": "leads_to", "case_id": "A058"}, {"source": "trigger_058", "target": "damage_058", "relation": "causes", "case_id": "A058"}, {"source": "case_059", "target": "hazard_059", "relation": "involves", "case_id": "A059"}, {"source": "hazard_059", "target": "warning_059", "relation": "precedes", "case_id": "A059"}, {"source": "warning_059", "target": "trigger_059", "relation": "leads_to", "case_id": "A059"}, {"source": "trigger_059", "target": "damage_059", "relation": "causes", "case_id": "A059"}, {"source": "case_060", "target": "hazard_060", "relation": "involves", "case_id": "A060"}, {"source": "hazard_060", "target": "warning_060", "relation": "precedes", "case_id": "A060"}, {"source": "warning_060", "target": "trigger_060", "relation": "leads_to", "case_id": "A060"}, {"source": "trigger_060", "target": "damage_060", "relation": "causes", "case_id": "A060"}, {"source": "case_061", "target": "hazard_061", "relation": "involves", "case_id": "A061"}, {"source": "hazard_061", "target": "warning_061", "relation": "precedes", "case_id": "A061"}, {"source": "warning_061", "target": "trigger_061", "relation": "leads_to", "case_id": "A061"}, {"source": "trigger_061", "target": "damage_061", "relation": "causes", "case_id": "A061"}, {"source": "case_062", "target": "hazard_062", "relation": "involves", "case_id": "A062"}, {"source": "hazard_062", "target": "warning_062", "relation": "precedes", "case_id": "A062"}, {"source": "warning_062", "target": "trigger_062", "relation": "leads_to", "case_id": "A062"}, {"source": "trigger_062", "target": "damage_062", "relation": "causes", "case_id": "A062"}, {"source": "case_063", "target": "hazard_063", "relation": "involves", "case_id": "A063"}, {"source": "hazard_063", "target": "warning_063", "relation": "precedes", "case_id": "A063"}, {"source": "warning_063", "target": "trigger_063", "relation": "leads_to", "case_id": "A063"}, {"source": "trigger_063", "target": "damage_063", "relation": "causes", "case_id": "A063"}, {"source": "case_065", "target": "hazard_065", "relation": "involves", "case_id": "A065"}, {"source": "hazard_065", "target": "warning_065", "relation": "precedes", "case_id": "A065"}, {"source": "warning_065", "target": "trigger_065", "relation": "leads_to", "case_id": "A065"}, {"source": "trigger_065", "target": "damage_065", "relation": "causes", "case_id": "A065"}, {"source": "case_033", "target": "hazard_033", "relation": "involves", "case_id": "A033"}, {"source": "hazard_033", "target": "warning_033", "relation": "precedes", "case_id": "A033"}, {"source": "warning_033", "target": "trigger_033", "relation": "leads_to", "case_id": "A033"}, {"source": "trigger_033", "target": "damage_033", "relation": "causes", "case_id": "A033"}, {"source": "case_034", "target": "hazard_034", "relation": "involves", "case_id": "A034"}, {"source": "hazard_034", "target": "warning_034", "relation": "precedes", "case_id": "A034"}, {"source": "warning_034", "target": "trigger_034", "relation": "leads_to", "case_id": "A034"}, {"source": "trigger_034", "target": "damage_034", "relation": "causes", "case_id": "A034"}, {"source": "case_035", "target": "hazard_035", "relation": "involves", "case_id": "A035"}, {"source": "hazard_035", "target": "warning_035", "relation": "precedes", "case_id": "A035"}, {"source": "warning_035", "target": "trigger_035", "relation": "leads_to", "case_id": "A035"}, {"source": "trigger_035", "target": "damage_035", "relation": "causes", "case_id": "A035"}, {"source": "case_036", "target": "hazard_036", "relation": "involves", "case_id": "A036"}, {"source": "hazard_036", "target": "warning_036", "relation": "precedes", "case_id": "A036"}, {"source": "warning_036", "target": "trigger_036", "relation": "leads_to", "case_id": "A036"}, {"source": "trigger_036", "target": "damage_036", "relation": "causes", "case_id": "A036"}, {"source": "case_037", "target": "hazard_037", "relation": "involves", "case_id": "A037"}, {"source": "hazard_037", "target": "warning_037", "relation": "precedes", "case_id": "A037"}, {"source": "warning_037", "target": "trigger_037", "relation": "leads_to", "case_id": "A037"}, {"source": "trigger_037", "target": "damage_037", "relation": "causes", "case_id": "A037"}, {"source": "case_039", "target": "hazard_039", "relation": "involves", "case_id": "A039"}, {"source": "hazard_039", "target": "warning_039", "relation": "precedes", "case_id": "A039"}, {"source": "warning_039", "target": "trigger_039", "relation": "leads_to", "case_id": "A039"}, {"source": "trigger_039", "target": "damage_039", "relation": "causes", "case_id": "A039"}, {"source": "case_040", "target": "hazard_040", "relation": "involves", "case_id": "A040"}, {"source": "hazard_040", "target": "warning_040", "relation": "precedes", "case_id": "A040"}, {"source": "warning_040", "target": "trigger_040", "relation": "leads_to", "case_id": "A040"}, {"source": "trigger_040", "target": "damage_040", "relation": "causes", "case_id": "A040"}, {"source": "case_041", "target": "hazard_041", "relation": "involves", "case_id": "A041"}, {"source": "hazard_041", "target": "warning_041", "relation": "precedes", "case_id": "A041"}, {"source": "warning_041", "target": "trigger_041", "relation": "leads_to", "case_id": "A041"}, {"source": "trigger_041", "target": "damage_041", "relation": "causes", "case_id": "A041"}, {"source": "case_043", "target": "hazard_043", "relation": "involves", "case_id": "A043"}, {"source": "hazard_043", "target": "warning_043", "relation": "precedes", "case_id": "A043"}, {"source": "warning_043", "target": "trigger_043", "relation": "leads_to", "case_id": "A043"}, {"source": "trigger_043", "target": "damage_043", "relation": "causes", "case_id": "A043"}, {"source": "case_047", "target": "hazard_047", "relation": "involves", "case_id": "A047"}, {"source": "hazard_047", "target": "warning_047", "relation": "precedes", "case_id": "A047"}, {"source": "warning_047", "target": "trigger_047", "relation": "leads_to", "case_id": "A047"}, {"source": "trigger_047", "target": "damage_047", "relation": "causes", "case_id": "A047"}, {"source": "case_051", "target": "hazard_051", "relation": "involves", "case_id": "A051"}, {"source": "hazard_051", "target": "warning_051", "relation": "precedes", "case_id": "A051"}, {"source": "warning_051", "target": "trigger_051", "relation": "leads_to", "case_id": "A051"}, {"source": "trigger_051", "target": "damage_051", "relation": "causes", "case_id": "A051"}, {"source": "case_052", "target": "hazard_052", "relation": "involves", "case_id": "A052"}, {"source": "hazard_052", "target": "warning_052", "relation": "precedes", "case_id": "A052"}, {"source": "warning_052", "target": "trigger_052", "relation": "leads_to", "case_id": "A052"}, {"source": "trigger_052", "target": "damage_052", "relation": "causes", "case_id": "A052"}, {"source": "case_055", "target": "hazard_055", "relation": "involves", "case_id": "A055"}, {"source": "hazard_055", "target": "warning_055", "relation": "precedes", "case_id": "A055"}, {"source": "warning_055", "target": "trigger_055", "relation": "leads_to", "case_id": "A055"}, {"source": "trigger_055", "target": "damage_055", "relation": "causes", "case_id": "A055"}, {"source": "case_056", "target": "hazard_056", "relation": "involves", "case_id": "A056"}, {"source": "hazard_056", "target": "warning_056", "relation": "precedes", "case_id": "A056"}, {"source": "warning_056", "target": "trigger_056", "relation": "leads_to", "case_id": "A056"}, {"source": "trigger_056", "target": "damage_056", "relation": "causes", "case_id": "A056"}, {"source": "case_057", "target": "hazard_057", "relation": "involves", "case_id": "A057"}, {"source": "hazard_057", "target": "warning_057", "relation": "precedes", "case_id": "A057"}, {"source": "warning_057", "target": "trigger_057", "relation": "leads_to", "case_id": "A057"}, {"source": "trigger_057", "target": "damage_057", "relation": "causes", "case_id": "A057"}, {"source": "case_066", "target": "hazard_066", "relation": "involves", "case_id": "A066"}, {"source": "hazard_066", "target": "warning_066", "relation": "precedes", "case_id": "A066"}, {"source": "warning_066", "target": "trigger_066", "relation": "leads_to", "case_id": "A066"}, {"source": "trigger_066", "target": "damage_066", "relation": "causes", "case_id": "A066"}, {"source": "case_067", "target": "hazard_067", "relation": "involves", "case_id": "A067"}, {"source": "hazard_067", "target": "warning_067", "relation": "precedes", "case_id": "A067"}, {"source": "warning_067", "target": "trigger_067", "relation": "leads_to", "case_id": "A067"}, {"source": "trigger_067", "target": "damage_067", "relation": "causes", "case_id": "A067"}, {"source": "case_068", "target": "hazard_068", "relation": "involves", "case_id": "A068"}, {"source": "hazard_068", "target": "warning_068", "relation": "precedes", "case_id": "A068"}, {"source": "warning_068", "target": "trigger_068", "relation": "leads_to", "case_id": "A068"}, {"source": "trigger_068", "target": "damage_068", "relation": "causes", "case_id": "A068"}, {"source": "case_069", "target": "hazard_069", "relation": "involves", "case_id": "A069"}, {"source": "hazard_069", "target": "warning_069", "relation": "precedes", "case_id": "A069"}, {"source": "warning_069", "target": "trigger_069", "relation": "leads_to", "case_id": "A069"}, {"source": "trigger_069", "target": "damage_069", "relation": "causes", "case_id": "A069"}, {"source": "case_070", "target": "hazard_070", "relation": "involves", "case_id": "A070"}, {"source": "hazard_070", "target": "warning_070", "relation": "precedes", "case_id": "A070"}, {"source": "warning_070", "target": "trigger_070", "relation": "leads_to", "case_id": "A070"}, {"source": "trigger_070", "target": "damage_070", "relation": "causes", "case_id": "A070"}, {"source": "case_071", "target": "hazard_071", "relation": "involves", "case_id": "A071"}, {"source": "hazard_071", "target": "warning_071", "relation": "precedes", "case_id": "A071"}, {"source": "warning_071", "target": "trigger_071", "relation": "leads_to", "case_id": "A071"}, {"source": "trigger_071", "target": "damage_071", "relation": "causes", "case_id": "A071"}, {"source": "case_072", "target": "hazard_072", "relation": "involves", "case_id": "A072"}, {"source": "hazard_072", "target": "warning_072", "relation": "precedes", "case_id": "A072"}, {"source": "warning_072", "target": "trigger_072", "relation": "leads_to", "case_id": "A072"}, {"source": "trigger_072", "target": "damage_072", "relation": "causes", "case_id": "A072"}, {"source": "case_073", "target": "hazard_073", "relation": "involves", "case_id": "A073"}, {"source": "hazard_073", "target": "warning_073", "relation": "precedes", "case_id": "A073"}, {"source": "warning_073", "target": "trigger_073", "relation": "leads_to", "case_id": "A073"}, {"source": "trigger_073", "target": "damage_073", "relation": "causes", "case_id": "A073"}, {"source": "case_075", "target": "hazard_075", "relation": "involves", "case_id": "A075"}, {"source": "hazard_075", "target": "warning_075", "relation": "precedes", "case_id": "A075"}, {"source": "warning_075", "target": "trigger_075", "relation": "leads_to", "case_id": "A075"}, {"source": "trigger_075", "target": "damage_075", "relation": "causes", "case_id": "A075"}, {"source": "case_079", "target": "hazard_079", "relation": "involves", "case_id": "A079"}, {"source": "hazard_079", "target": "warning_079", "relation": "precedes", "case_id": "A079"}, {"source": "warning_079", "target": "trigger_079", "relation": "leads_to", "case_id": "A079"}, {"source": "trigger_079", "target": "damage_079", "relation": "causes", "case_id": "A079"}, {"source": "case_081", "target": "hazard_081", "relation": "involves", "case_id": "A081"}, {"source": "hazard_081", "target": "warning_081", "relation": "precedes", "case_id": "A081"}, {"source": "warning_081", "target": "trigger_081", "relation": "leads_to", "case_id": "A081"}, {"source": "trigger_081", "target": "damage_081", "relation": "causes", "case_id": "A081"}, {"source": "case_082", "target": "hazard_082", "relation": "involves", "case_id": "A082"}, {"source": "hazard_082", "target": "warning_082", "relation": "precedes", "case_id": "A082"}, {"source": "warning_082", "target": "trigger_082", "relation": "leads_to", "case_id": "A082"}, {"source": "trigger_082", "target": "damage_082", "relation": "causes", "case_id": "A082"}, {"source": "case_083", "target": "hazard_083", "relation": "involves", "case_id": "A083"}, {"source": "hazard_083", "target": "warning_083", "relation": "precedes", "case_id": "A083"}, {"source": "warning_083", "target": "trigger_083", "relation": "leads_to", "case_id": "A083"}, {"source": "trigger_083", "target": "damage_083", "relation": "causes", "case_id": "A083"}, {"source": "case_084", "target": "hazard_084", "relation": "involves", "case_id": "A084"}, {"source": "hazard_084", "target": "warning_084", "relation": "precedes", "case_id": "A084"}, {"source": "warning_084", "target": "trigger_084", "relation": "leads_to", "case_id": "A084"}, {"source": "trigger_084", "target": "damage_084", "relation": "causes", "case_id": "A084"}, {"source": "case_085", "target": "hazard_085", "relation": "involves", "case_id": "A085"}, {"source": "hazard_085", "target": "warning_085", "relation": "precedes", "case_id": "A085"}, {"source": "warning_085", "target": "trigger_085", "relation": "leads_to", "case_id": "A085"}, {"source": "trigger_085", "target": "damage_085", "relation": "causes", "case_id": "A085"}, {"source": "case_086", "target": "hazard_086", "relation": "involves", "case_id": "A086"}, {"source": "hazard_086", "target": "warning_086", "relation": "precedes", "case_id": "A086"}, {"source": "warning_086", "target": "trigger_086", "relation": "leads_to", "case_id": "A086"}, {"source": "trigger_086", "target": "damage_086", "relation": "causes", "case_id": "A086"}, {"source": "case_088", "target": "hazard_088", "relation": "involves", "case_id": "A088"}, {"source": "hazard_088", "target": "warning_088", "relation": "precedes", "case_id": "A088"}, {"source": "warning_088", "target": "trigger_088", "relation": "leads_to", "case_id": "A088"}, {"source": "trigger_088", "target": "damage_088", "relation": "causes", "case_id": "A088"}, {"source": "case_089", "target": "hazard_089", "relation": "involves", "case_id": "A089"}, {"source": "hazard_089", "target": "warning_089", "relation": "precedes", "case_id": "A089"}, {"source": "warning_089", "target": "trigger_089", "relation": "leads_to", "case_id": "A089"}, {"source": "trigger_089", "target": "damage_089", "relation": "causes", "case_id": "A089"}, {"source": "case_090", "target": "hazard_090", "relation": "involves", "case_id": "A090"}, {"source": "hazard_090", "target": "warning_090", "relation": "precedes", "case_id": "A090"}, {"source": "warning_090", "target": "trigger_090", "relation": "leads_to", "case_id": "A090"}, {"source": "trigger_090", "target": "damage_090", "relation": "causes", "case_id": "A090"}, {"source": "case_091", "target": "hazard_091", "relation": "involves", "case_id": "A091"}, {"source": "hazard_091", "target": "warning_091", "relation": "precedes", "case_id": "A091"}, {"source": "warning_091", "target": "trigger_091", "relation": "leads_to", "case_id": "A091"}, {"source": "trigger_091", "target": "damage_091", "relation": "causes", "case_id": "A091"}, {"source": "case_092", "target": "hazard_092", "relation": "involves", "case_id": "A092"}, {"source": "hazard_092", "target": "warning_092", "relation": "precedes", "case_id": "A092"}, {"source": "warning_092", "target": "trigger_092", "relation": "leads_to", "case_id": "A092"}, {"source": "trigger_092", "target": "damage_092", "relation": "causes", "case_id": "A092"}, {"source": "case_093", "target": "hazard_093", "relation": "involves", "case_id": "A093"}, {"source": "hazard_093", "target": "warning_093", "relation": "precedes", "case_id": "A093"}, {"source": "warning_093", "target": "trigger_093", "relation": "leads_to", "case_id": "A093"}, {"source": "trigger_093", "target": "damage_093", "relation": "causes", "case_id": "A093"}, {"source": "case_094", "target": "hazard_094", "relation": "involves", "case_id": "A094"}, {"source": "hazard_094", "target": "warning_094", "relation": "precedes", "case_id": "A094"}, {"source": "warning_094", "target": "trigger_094", "relation": "leads_to", "case_id": "A094"}, {"source": "trigger_094", "target": "damage_094", "relation": "causes", "case_id": "A094"}, {"source": "case_095", "target": "hazard_095", "relation": "involves", "case_id": "A095"}, {"source": "hazard_095", "target": "warning_095", "relation": "precedes", "case_id": "A095"}, {"source": "warning_095", "target": "trigger_095", "relation": "leads_to", "case_id": "A095"}, {"source": "trigger_095", "target": "damage_095", "relation": "causes", "case_id": "A095"}, {"source": "case_096", "target": "hazard_096", "relation": "involves", "case_id": "A096"}, {"source": "hazard_096", "target": "warning_096", "relation": "precedes", "case_id": "A096"}, {"source": "warning_096", "target": "trigger_096", "relation": "leads_to", "case_id": "A096"}, {"source": "trigger_096", "target": "damage_096", "relation": "causes", "case_id": "A096"}, {"source": "case_097", "target": "hazard_097", "relation": "involves", "case_id": "A097"}, {"source": "hazard_097", "target": "warning_097", "relation": "precedes", "case_id": "A097"}, {"source": "warning_097", "target": "trigger_097", "relation": "leads_to", "case_id": "A097"}, {"source": "trigger_097", "target": "damage_097", "relation": "causes", "case_id": "A097"}, {"source": "case_098", "target": "hazard_098", "relation": "involves", "case_id": "A098"}, {"source": "hazard_098", "target": "warning_098", "relation": "precedes", "case_id": "A098"}, {"source": "warning_098", "target": "trigger_098", "relation": "leads_to", "case_id": "A098"}, {"source": "trigger_098", "target": "damage_098", "relation": "causes", "case_id": "A098"}, {"source": "case_099", "target": "hazard_099", "relation": "involves", "case_id": "A099"}, {"source": "hazard_099", "target": "warning_099", "relation": "precedes", "case_id": "A099"}, {"source": "warning_099", "target": "trigger_099", "relation": "leads_to", "case_id": "A099"}, {"source": "trigger_099", "target": "damage_099", "relation": "causes", "case_id": "A099"}, {"source": "case_100", "target": "hazard_100", "relation": "involves", "case_id": "A100"}, {"source": "hazard_100", "target": "warning_100", "relation": "precedes", "case_id": "A100"}, {"source": "warning_100", "target": "trigger_100", "relation": "leads_to", "case_id": "A100"}, {"source": "trigger_100", "target": "damage_100", "relation": "causes", "case_id": "A100"}, {"source": "case_021", "target": "hazard_021", "relation": "involves", "case_id": "A021"}, {"source": "hazard_021", "target": "warning_021", "relation": "precedes", "case_id": "A021"}, {"source": "warning_021", "target": "trigger_021", "relation": "leads_to", "case_id": "A021"}, {"source": "trigger_021", "target": "damage_021", "relation": "causes", "case_id": "A021"}, {"source": "case_022", "target": "hazard_022", "relation": "involves", "case_id": "A022"}, {"source": "hazard_022", "target": "warning_022", "relation": "precedes", "case_id": "A022"}, {"source": "warning_022", "target": "trigger_022", "relation": "leads_to", "case_id": "A022"}, {"source": "trigger_022", "target": "damage_022", "relation": "causes", "case_id": "A022"}, {"source": "case_023", "target": "hazard_023", "relation": "involves", "case_id": "A023"}, {"source": "hazard_023", "target": "warning_023", "relation": "precedes", "case_id": "A023"}, {"source": "warning_023", "target": "trigger_023", "relation": "leads_to", "case_id": "A023"}, {"source": "trigger_023", "target": "damage_023", "relation": "causes", "case_id": "A023"}, {"source": "case_024", "target": "hazard_024", "relation": "involves", "case_id": "A024"}, {"source": "hazard_024", "target": "warning_024", "relation": "precedes", "case_id": "A024"}, {"source": "warning_024", "target": "trigger_024", "relation": "leads_to", "case_id": "A024"}, {"source": "trigger_024", "target": "damage_024", "relation": "causes", "case_id": "A024"}, {"source": "case_025", "target": "hazard_025", "relation": "involves", "case_id": "A025"}, {"source": "hazard_025", "target": "warning_025", "relation": "precedes", "case_id": "A025"}, {"source": "warning_025", "target": "trigger_025", "relation": "leads_to", "case_id": "A025"}, {"source": "trigger_025", "target": "damage_025", "relation": "causes", "case_id": "A025"}, {"source": "case_026", "target": "hazard_026", "relation": "involves", "case_id": "A026"}, {"source": "hazard_026", "target": "warning_026", "relation": "precedes", "case_id": "A026"}, {"source": "warning_026", "target": "trigger_026", "relation": "leads_to", "case_id": "A026"}, {"source": "trigger_026", "target": "damage_026", "relation": "causes", "case_id": "A026"}, {"source": "case_027", "target": "hazard_027", "relation": "involves", "case_id": "A027"}, {"source": "hazard_027", "target": "warning_027", "relation": "precedes", "case_id": "A027"}, {"source": "warning_027", "target": "trigger_027", "relation": "leads_to", "case_id": "A027"}, {"source": "trigger_027", "target": "damage_027", "relation": "causes", "case_id": "A027"}, {"source": "case_028", "target": "hazard_028", "relation": "involves", "case_id": "A028"}, {"source": "hazard_028", "target": "warning_028", "relation": "precedes", "case_id": "A028"}, {"source": "warning_028", "target": "trigger_028", "relation": "leads_to", "case_id": "A028"}, {"source": "trigger_028", "target": "damage_028", "relation": "causes", "case_id": "A028"}, {"source": "case_029", "target": "hazard_029", "relation": "involves", "case_id": "A029"}, {"source": "hazard_029", "target": "warning_029", "relation": "precedes", "case_id": "A029"}, {"source": "warning_029", "target": "trigger_029", "relation": "leads_to", "case_id": "A029"}, {"source": "trigger_029", "target": "damage_029", "relation": "causes", "case_id": "A029"}, {"source": "case_030", "target": "hazard_030", "relation": "involves", "case_id": "A030"}, {"source": "hazard_030", "target": "warning_030", "relation": "precedes", "case_id": "A030"}, {"source": "warning_030", "target": "trigger_030", "relation": "leads_to", "case_id": "A030"}, {"source": "trigger_030", "target": "damage_030", "relation": "causes", "case_id": "A030"}]}