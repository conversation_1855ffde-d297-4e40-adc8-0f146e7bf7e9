#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
进度条工具模块
提供统一的进度条显示功能，用于各个处理阶段
"""

import logging
from typing import Optional


class ProgressBar:
    """
    统一的进度条工具类
    
    提供简单的字符进度条显示功能，支持自定义样式和日志输出
    """
    
    def __init__(
        self, 
        total: int, 
        stage_name: str, 
        bar_length: int = 20, 
        logger: Optional[logging.Logger] = None,
        update_threshold: int = 1
    ):
        """
        初始化进度条
        
        Args:
            total: 总数量
            stage_name: 阶段名称
            bar_length: 进度条长度（字符数）
            logger: 日志记录器，如果为None则使用print输出
            update_threshold: 更新阈值，百分比变化超过此值才显示
        """
        self.total = max(1, total)  # 避免除零错误
        self.current = 0
        self.stage_name = stage_name
        self.bar_length = bar_length
        self.logger = logger
        self.update_threshold = update_threshold
        self.last_percentage = -1  # 避免重复显示相同百分比
        
        # 显示初始状态
        self._display_progress(0)
    
    def update(self, current: Optional[int] = None, increment: int = 1) -> None:
        """
        更新进度
        
        Args:
            current: 当前完成数量，如果为None则使用increment增量
            increment: 增量，当current为None时使用
        """
        if current is not None:
            self.current = min(current, self.total)  # 确保不超过总数
        else:
            self.current = min(self.current + increment, self.total)
        
        # 计算百分比
        percentage = int((self.current / self.total) * 100)
        
        # 只在百分比变化超过阈值时显示，避免过多日志
        if abs(percentage - self.last_percentage) >= self.update_threshold or self.current == self.total:
            self._display_progress(percentage)
            self.last_percentage = percentage
    
    def _display_progress(self, percentage: int) -> None:
        """
        显示进度条
        
        Args:
            percentage: 当前百分比
        """
        # 计算进度条填充长度
        filled_length = int(self.bar_length * self.current / self.total)
        
        # 构建进度条字符串 - 使用边界清晰的字符
        bar = '■' * filled_length + '□' * (self.bar_length - filled_length)

        # 格式化显示文本 - 添加边框让进度条更清晰
        progress_text = f"[{self.stage_name}] |{bar}| {percentage}% ({self.current}/{self.total})"
        
        # 输出进度信息
        if self.logger:
            self.logger.info(progress_text)
        else:
            print(progress_text)
    
    def finish(self) -> None:
        """
        完成进度，确保显示100%
        """
        self.current = self.total
        self._display_progress(100)
        
        # 显示完成信息
        completion_text = f"[{self.stage_name}] 完成！共处理 {self.total} 项"
        if self.logger:
            self.logger.info(completion_text)
        else:
            print(completion_text)
    
    def set_total(self, new_total: int) -> None:
        """
        更新总数量（用于动态确定总数的场景）
        
        Args:
            new_total: 新的总数量
        """
        self.total = max(1, new_total)
        # 重新计算并显示当前进度
        percentage = int((self.current / self.total) * 100)
        self._display_progress(percentage)
        self.last_percentage = percentage


def create_progress_bar(
    total: int, 
    stage_name: str, 
    logger: Optional[logging.Logger] = None,
    bar_length: int = 20,
    update_threshold: int = 1
) -> ProgressBar:
    """
    创建进度条的便捷函数
    
    Args:
        total: 总数量
        stage_name: 阶段名称
        logger: 日志记录器
        bar_length: 进度条长度
        update_threshold: 更新阈值
        
    Returns:
        ProgressBar: 进度条实例
    """
    return ProgressBar(
        total=total,
        stage_name=stage_name,
        logger=logger,
        bar_length=bar_length,
        update_threshold=update_threshold
    )
