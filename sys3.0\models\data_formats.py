"""
统一数据格式模型
根据数据流整改方案定义的标准数据结构
"""

from typing import List, Optional, Dict, Any, TypedDict

# 微博数据项 - 信息收集智能体输出格式
class WeiboItem(TypedDict, total=False):
    """
    微博数据项 - 信息收集智能体的标准输出格式

    字段说明：
    - weibo_id: 微博ID
    - weibo_url: 微博地址
    - timestamp: 发布时间
    - location: 发布地点
    - author: 作者信息
    - content: 微博文本内容
    - images: 图片地址列表
    - videos: 视频地址列表
    """
    weibo_id: str
    weibo_url: str
    timestamp: str
    location: str
    author: str
    content: str
    images: List[str]
    videos: List[str]

# 媒体项（图片或视频）
class MediaItem(TypedDict, total=False):
    """
    媒体项结构

    字段说明：
    - url: 媒体文件地址
    - description: 媒体描述（预留字段，暂不实现）
    """
    url: str
    description: Optional[str]

# 隐患数据项 - 信息抽取智能体输出格式
class HazardItem(TypedDict, total=False):
    """
    隐患数据项 - 信息抽取智能体的标准输出格式

    字段说明：
    - hazard_id: 隐患ID，格式如"HZ-20240601-001"
    - weibo_id: 微博ID
    - weibo_url: 微博地址
    - timestamp: 发布时间
    - location: 发布地点
    - author: 作者信息
    - hazard_description: 根据微博文本内容生成的隐患描述
    - images: 图片列表，包含描述字段
    - videos: 视频列表，包含描述字段
    """
    hazard_id: str
    weibo_id: str
    weibo_url: str
    timestamp: str
    location: str
    author: str
    hazard_description: str
    images: List[MediaItem]
    videos: List[MediaItem]

# 风险评估结果 - 风险评估智能体输出格式
class RiskAssessment(TypedDict, total=False):
    """
    风险评估结果 - 风险评估智能体的标准输出格式

    字段说明：
    - hazard_id: 隐患ID
    - weibo_id: 微博ID
    - weibo_url: 微博地址
    - timestamp: 发布时间
    - location: 发布地点
    - author: 作者信息
    - hazard_description: 隐患描述
    - risk_level: 风险等级
    - violation: 是否违规
    - graph_path: 图谱路径
    - law_reason: 法规命中说明
    - evidence_chain: 证据链说明
    - images: 图片列表
    - videos: 视频列表
    - report_markdown: 单个隐患的报告段落
    """
    hazard_id: str
    weibo_id: str
    weibo_url: str
    timestamp: str
    location: str
    author: str
    hazard_description: str
    risk_level: str
    violation: bool
    graph_path: str
    law_reason: str
    evidence_chain: str
    images: List[MediaItem]
    videos: List[MediaItem]
    report_markdown: str

# 任务结果 - 控制器智能体输出格式
class TaskResult(TypedDict, total=False):
    """
    任务结果 - 控制器智能体的标准输出格式

    字段说明：
    - task_id: 任务ID
    - status: 任务状态
    - summary: 任务摘要
    - risk_groups: 按风险等级分组的评估结果
    - report_markdown: 完整的Markdown格式报告
    - created_at: 创建时间
    """
    task_id: str
    status: str
    summary: str
    risk_groups: Dict[str, List[RiskAssessment]]
    report_markdown: str
    created_at: str