"""
知识图谱引擎核心模块
提供图数据库操作功能
"""

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import time

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent.parent  # 指向sys3.0目录
sys.path.insert(0, str(ROOT_DIR))

from config.config import config_service
from utils.logger import LoggerMixin
from utils.exceptions import SystemException, ERR_NETWORK
from tools import ToolInterface

from .database import Neo4jConnection

class GraphEngine(LoggerMixin, ToolInterface):
    """知识图谱引擎主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, max_retries=3, retry_delay=2):
        """
        初始化知识图谱引擎，使用统一配置
        
        Args:
            config: 知识图谱配置，如果为None则使用默认配置
            max_retries: 最大重试次数
            retry_delay: 重试延迟(秒)
        """
        LoggerMixin.__init__(self)
        ToolInterface.__init__(self, config)
        
        # 如果没有提供配置，使用配置服务获取
        if config is None:
            config = config_service.get_kg_config()
        
        self.config = config
        
        # 初始化数据库连接
        self.connection = Neo4jConnection(
            uri=self.config['neo4j_uri'],
            user=self.config['neo4j_user'],
            password=self.config['neo4j_password']
        )

        # 自动加载图谱数据
        self._auto_load_graph_data()

        # 连接数据库(带重试)
        self._connect_with_retry(max_retries, retry_delay)
            
    def _connect_with_retry(self, max_retries=3, retry_delay=2):
        """
        带重试机制的数据库连接
        
        Args:
            max_retries: 最大重试次数
            retry_delay: 重试间隔(秒)
            
        Raises:
            SystemException: 连接失败且重试次数用尽时抛出
        """
        attempts = 0
        last_error = None
        
        while attempts < max_retries:
            try:
                self.log_info(f"尝试连接Neo4j数据库(尝试 {attempts+1}/{max_retries})")
                if self.connection.connect():
                    self.log_info("Neo4j数据库连接成功")

                    # 连接成功后检查数据库是否为空，如果为空则自动加载知识图谱
                    self._check_and_load_data()

                    return  # 连接成功，退出循环
                else:
                    raise Exception("数据库连接失败")
            except Exception as e:
                last_error = e
                attempts += 1
                if attempts < max_retries:
                    self.log_warning(
                        f"Neo4j连接失败 (尝试 {attempts}/{max_retries}): {str(e)}. "
                        f"将在 {retry_delay} 秒后重试..."
                    )
                    time.sleep(retry_delay)
                    # 增加重试延迟，避免频繁请求
                    retry_delay = min(retry_delay * 1.5, 10)
        
        # 重试次数用尽，仍然失败
        error_msg = (
            f"Neo4j数据库连接失败，已重试{max_retries}次: {str(last_error)}. "
            f"请检查: 1) Neo4j服务是否运行; 2) 连接信息是否正确; "
            f"3) 防火墙是否允许连接; 4) 数据库密码是否正确"
        )
        self.log_error(error_msg)
        
        # 抛出自定义异常，包含详细错误信息和排查建议
        raise SystemException(f"Neo4j数据库连接失败: {error_msg}", code=ERR_NETWORK, uri=self.config['neo4j_uri'], user=self.config['neo4j_user'])
    
    def _check_and_load_data(self):
        """
        检查数据库是否为空，如果为空则自动加载知识图谱数据
        """
        try:
            # 查询节点数量
            result = self.connection.execute_query("MATCH (n) RETURN count(n) as count")
            node_count = result[0]["count"] if result else 0
            
            if node_count == 0:
                self.log_info("数据库为空，自动加载知识图谱数据")
                self.load_knowledge_graph()
                self.log_info("知识图谱数据加载完成")
            else:
                self.log_info(f"数据库中已有 {node_count} 个节点，跳过自动加载")
        except Exception as e:
            self.log_warning(f"检查数据库节点数量失败: {str(e)}，无法自动加载知识图谱数据")
    
    def load_knowledge_graph(self, data_file: Optional[str] = None) -> None:
        """
        加载知识图谱数据 - 现在使用智能加载器

        Args:
            data_file: 数据文件路径（已废弃，使用智能加载器自动处理）
        """
        try:
            self.log_info("使用智能加载器重新加载图谱数据")
            from tools.graph_engine.smart_auto_loader import SmartGraphAutoLoader

            # 使用智能加载器强制重新加载
            loader = SmartGraphAutoLoader(
                uri=self.config['neo4j_uri'],
                user=self.config['neo4j_user'],
                password=self.config['neo4j_password']
            )

            success = loader.smart_auto_load(force_reload=True)
            loader.close()

            if not success:
                raise Exception("智能加载器重新加载失败")

            self.log_info("图谱数据重新加载完成")

        except Exception as e:
            self.log_error(f"Failed to load knowledge graph: {str(e)}", e)
            raise
            
    def query_complete_paths_by_node(self, node_description: str, max_depth: Optional[int] = None,
                                    min_confidence: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        根据节点查询包含该节点的所有完整路径

        Args:
            node_description: 节点描述
            max_depth: 最大路径深度，默认使用配置值
            min_confidence: 最小置信度，默认使用配置值

        Returns:
            包含该节点的完整路径列表
        """
        # 使用配置中的默认值（如果未指定）
        if max_depth is None:
            max_depth = self.config.get('max_path_length', 5)
        if min_confidence is None:
            min_confidence = self.config.get('min_confidence', 0.75)

        # 使用语义搜索找到匹配的节点
        search_results = self.semantic_search(
            query=node_description,
            limit=5,
            min_score=min_confidence
        )

        # 为每个匹配的节点构建包含该节点的完整路径
        all_paths = []
        for result in search_results:
            # 使用实际的节点属性
            target_node_id = result.get('id', result.get('node', {}).get('id', '未知'))

            # 构建包含该节点的所有完整路径
            complete_paths = self._build_complete_paths_containing_node(
                target_node_id,
                max_depth,
                min_confidence
            )

            # 只添加真正的完整路径，如果没有找到完整路径则跳过
            if complete_paths:
                all_paths.extend(complete_paths)

        return all_paths

    def query_causal_paths(self, precursor: str, max_depth: Optional[int] = None,
                          min_confidence: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        查询因果路径（保持向后兼容）

        Args:
            precursor: 前兆事件
            max_depth: 最大路径深度，默认使用配置值
            min_confidence: 最小置信度，默认使用配置值

        Returns:
            因果路径列表
        """
        # 使用配置中的默认值（如果未指定）
        if max_depth is None:
            max_depth = self.config.get('max_path_length', 5)
        if min_confidence is None:
            min_confidence = self.config.get('min_confidence', 0.75)

        # 使用语义搜索找到匹配的起始节点
        search_results = self.semantic_search(
            query=precursor,
            limit=5,
            min_score=min_confidence
        )

        # 为每个匹配的节点构建包含该节点的完整路径
        all_paths = []
        for result in search_results:
            # 使用实际的节点属性
            target_node_id = result.get('id', result.get('node', {}).get('id', '未知'))

            # 构建包含该节点的所有完整路径
            complete_paths = self._build_complete_paths_containing_node(
                target_node_id,
                max_depth,
                min_confidence
            )

            # 只添加真正的完整路径，如果没有找到完整路径则跳过
            if complete_paths:
                all_paths.extend(complete_paths)

        return all_paths

    def _build_complete_paths_containing_node(self, target_node_id: str, max_depth: int, min_confidence: float) -> List[Dict[str, Any]]:
        """
        构建包含指定节点的所有完整路径

        Args:
            target_node_id: 目标节点ID
            max_depth: 最大路径深度
            min_confidence: 最小置信度（暂时忽略，因为关系中没有confidence属性）

        Returns:
            包含目标节点的完整路径列表
        """
        # 忽略 min_confidence 参数，保留以备将来使用
        _ = min_confidence

        try:
            # 构建 Cypher 查询来查找包含目标节点的完整因果链路径
            cypher_query = """
            // 找到包含目标节点的所有路径
            MATCH path = (start)-[:INVOLVES|PRECEDES|LEADS_TO|CAUSES*1..{max_depth}]->(end)
            WHERE $target_node_id IN [node in nodes(path) | node.id]

            // 扩展到完整路径：向前找到真正的起点
            OPTIONAL MATCH extended_start = (real_start)-[:INVOLVES|PRECEDES|LEADS_TO|CAUSES*0..3]->(start)
            WHERE NOT (real_start)<-[:INVOLVES|PRECEDES|LEADS_TO|CAUSES]-()

            // 扩展到完整路径：向后找到真正的终点
            OPTIONAL MATCH extended_end = (end)-[:INVOLVES|PRECEDES|LEADS_TO|CAUSES*0..3]->(real_end)
            WHERE NOT (real_end)-[:INVOLVES|PRECEDES|LEADS_TO|CAUSES]->()

            // 构建完整路径
            WITH CASE
                WHEN extended_start IS NOT NULL AND extended_end IS NOT NULL THEN
                    nodes(extended_start) + nodes(path)[1..] + nodes(extended_end)[1..]
                WHEN extended_start IS NOT NULL THEN
                    nodes(extended_start) + nodes(path)[1..]
                WHEN extended_end IS NOT NULL THEN
                    nodes(path) + nodes(extended_end)[1..]
                ELSE nodes(path)
            END as complete_path_nodes

            // 去重并返回
            WITH complete_path_nodes
            WHERE size(complete_path_nodes) > 1
            RETURN DISTINCT
                [node in complete_path_nodes | node.label] as path_nodes,
                [node in complete_path_nodes | node.id] as path_ids,
                [node in complete_path_nodes | node.type] as path_types,
                size(complete_path_nodes) as path_length
            ORDER BY path_length DESC
            LIMIT 50
            """.format(max_depth=max_depth)

            # 执行查询
            results = self.connection.execute_query(
                cypher_query,
                {
                    'target_node_id': target_node_id
                }
            )

            paths = []
            for result in results:
                path_nodes = result.get('path_nodes', [])
                path_ids = result.get('path_ids', [])
                path_types = result.get('path_types', [])
                path_length = result.get('path_length', 0)

                # 只返回完整路径（长度大于1）
                if path_nodes and len(path_nodes) > 1:
                    # 找到目标节点在路径中的位置
                    target_position = -1
                    for i, node_id in enumerate(path_ids):
                        if node_id == target_node_id:
                            target_position = i
                            break

                    # 创建带【】标记的节点显示列表
                    formatted_nodes = []
                    for i, (node_label, node_type) in enumerate(zip(path_nodes, path_types)):
                        if node_type == 'case':
                            formatted_nodes.append(f"【{node_label}】")
                        else:
                            formatted_nodes.append(node_label)

                    path = {
                        'nodes': path_nodes,  # 原始节点标签
                        'formatted_nodes': formatted_nodes,  # 带【】标记的节点
                        'node_ids': path_ids,
                        'node_types': path_types,
                        'path_length': path_length,
                        'target_position': target_position
                    }
                    paths.append(path)

            self.log_info(f"找到包含节点 '{target_node_id}' 的 {len(paths)} 条完整路径")
            return paths

        except Exception as e:
            self.log_warning(f"构建完整路径失败: {str(e)}")
            return []

    def get_reasoning_context(self, evidence_list: List[str],
                            max_depth: Optional[int] = None,
                            min_confidence: Optional[float] = None) -> Dict[str, Any]:
        """
        获取推理上下文

        Args:
            evidence_list: 证据列表
            max_depth: 最大路径深度，默认使用配置值
            min_confidence: 最小置信度，默认使用配置值

        Returns:
            推理上下文
        """
        # 使用配置中的默认值（如果未指定）
        if max_depth is None:
            max_depth = self.config['max_path_length']
        if min_confidence is None:
            min_confidence = self.config['min_confidence']

        context = {
            "causal_paths": []
        }

        for evidence in evidence_list:
            # 获取因果路径
            paths = self.query_causal_paths(
                precursor=evidence,
                max_depth=max_depth,
                min_confidence=min_confidence
            )
            context["causal_paths"].extend(paths)

        return context
        
    def query(self, cypher_query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行Cypher查询
        
        Args:
            cypher_query: Cypher查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        return self.connection.execute_query(cypher_query, params or {})
        
    def close(self) -> None:
        """关闭数据库连接"""
        self.connection.close()
        
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        _ = exc_type, exc_val, exc_tb  # 忽略未使用的参数
        self.close()
        return False  # 不抑制异常

    def execute(self, input_data):
        """执行图谱查询

        Args:
            input_data: 包含查询参数的字典，格式如下:
                {
                    'query_type': '查询类型', # 可选值: 'causal_paths', 'complete_paths_by_node', 'reasoning_context'
                    'start_event': '起始事件', # 用于causal_paths查询
                    'node_description': '节点描述', # 用于complete_paths_by_node查询
                    'evidence': ['证据1', '证据2'] # 用于reasoning_context查询
                }

        Returns:
            查询结果
        """
        query_type = input_data.get('query_type')
        
        if not query_type:
            return {"error": "缺少查询类型参数"}
        
        try:
            if query_type == 'causal_paths':
                start_event = input_data.get('start_event')
                max_depth = input_data.get('max_depth')
                min_confidence = input_data.get('min_confidence')
                if not start_event:
                    return {"error": "缺少起始事件参数"}
                return self.query_causal_paths(
                    precursor=start_event,
                    max_depth=max_depth or self.config['max_path_length'],
                    min_confidence=min_confidence or self.config.get('min_confidence', 0.75)
                )

            elif query_type == 'complete_paths_by_node':
                node_description = input_data.get('node_description')
                max_depth = input_data.get('max_depth')
                min_confidence = input_data.get('min_confidence')
                if not node_description:
                    return {"error": "缺少节点描述参数"}
                return self.query_complete_paths_by_node(
                    node_description=node_description,
                    max_depth=max_depth or self.config['max_path_length'],
                    min_confidence=min_confidence or self.config.get('min_confidence', 0.75)
                )

            elif query_type == 'reasoning_context':
                evidence = input_data.get('evidence')
                if not evidence:
                    return {"error": "缺少证据参数"}
                return self.get_reasoning_context(evidence)
                
            else:
                return {"error": f"未知的查询类型: {query_type}"}
                
        except Exception as e:
            self.log_error(f"执行图谱查询失败: {str(e)}")
            return {"error": f"执行图谱查询失败: {str(e)}"}

    def _auto_load_graph_data(self):
        """智能自动加载图谱数据"""
        try:
            from tools.graph_engine.smart_auto_loader import SmartGraphAutoLoader

            # 使用当前连接配置创建智能加载器
            loader = SmartGraphAutoLoader(
                uri=self.config['neo4j_uri'],
                user=self.config['neo4j_user'],
                password=self.config['neo4j_password']
            )

            # 智能检查并自动加载数据
            success = loader.smart_auto_load(force_reload=False)
            if success:
                self.log_info("✅ 图谱数据智能加载完成")
            else:
                self.log_warning("⚠️ 图谱数据智能加载失败")

            loader.close()

        except Exception as e:
            self.log_warning(f"⚠️ 图谱数据智能加载出错: {e}")
            # 不抛出异常，允许系统继续运行

    def find_causal_paths(self, description: str, max_depth: int = 2, min_confidence: float = 0.5) -> Dict[str, Any]:
        """
        查找因果路径

        Args:
            description: 描述文本
            max_depth: 最大路径深度
            min_confidence: 最小置信度

        Returns:
            Dict: 包含因果路径的结果
        """
        try:
            self.log_info(f"查找因果路径: {description}")

            # 使用新的语义搜索功能
            paths = self.query_causal_paths(
                precursor=description,
                max_depth=max_depth,
                min_confidence=min_confidence
            )

            result = {"causal_paths": paths}

            self.log_info(f"找到 {len(result.get('causal_paths', []))} 条因果路径")
            return result

        except Exception as e:
            self.log_error(f"查找因果路径失败: {str(e)}")
            return {"error": f"查找因果路径失败: {str(e)}"}


    def query_graph(self, query: str, query_type: str = "comprehensive") -> Dict[str, Any]:
        """
        综合图谱查询

        Args:
            query: 查询文本
            query_type: 查询类型 (causal_paths, comprehensive)

        Returns:
            Dict: 查询结果
        """
        try:
            self.log_info(f"执行图谱查询: {query} (类型: {query_type})")

            if query_type == "causal_paths" or query_type == "comprehensive":
                causal_result = self.find_causal_paths(query)
                return {
                    "query": query,
                    "causal_paths": causal_result.get("causal_paths", []),
                    "timestamp": self._get_timestamp()
                }
            else:
                return {"error": f"不支持的查询类型: {query_type}"}

        except Exception as e:
            self.log_error(f"图谱查询失败: {str(e)}")
            return {"error": f"图谱查询失败: {str(e)}"}

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

    def semantic_search(self, query: str, limit: int = 5, min_score: float = 0.75) -> List[Dict[str, Any]]:
        """
        语义搜索

        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            min_score: 最小相似度分数

        Returns:
            搜索结果列表
        """
        try:
            # 导入嵌入服务
            from .embedding_service import get_embedding_service

            # 生成查询向量
            embedding_service = get_embedding_service()
            query_vector = embedding_service.encode_text(query)

            # 在所有节点类型中搜索（使用实际的节点标签）
            all_results = []
            node_types = ['Case', 'HazardBehavior', 'WarningSignal', 'TriggerCause', 'Damage']

            # 索引名称映射（使用实际的索引名称）
            index_mapping = {
                'Case': 'case_embeddings',
                'HazardBehavior': 'hazard_behavior_embeddings',
                'WarningSignal': 'warning_signal_embeddings',
                'TriggerCause': 'trigger_cause_embeddings',
                'Damage': 'damage_embeddings'
            }

            for node_type in node_types:
                index_name = index_mapping.get(node_type, f"{node_type.lower()}_embeddings")
                try:
                    results = self.connection.execute_vector_query(
                        index_name=index_name,
                        query_vector=query_vector,
                        top_k=limit,
                        min_score=min_score
                    )

                    # 处理结果格式并添加节点类型信息
                    for result in results:
                        node = result.get('node', {})
                        result['type'] = node_type
                        result['similarity'] = result.get('score', 0.0)
                        result['id'] = node.get('id', '未知')
                        result['label'] = node.get('label', '未知')
                        result['description'] = node.get('label', '无描述')

                    all_results.extend(results)

                except Exception as e:
                    self.log_warning(f"在{node_type}中搜索失败: {e}")
                    continue

            # 按相似度排序并限制结果数量
            all_results.sort(key=lambda x: x.get('similarity', 0.0), reverse=True)
            return all_results[:limit]

        except Exception as e:
            self.log_error(f"语义搜索失败: {str(e)}")
            return []
