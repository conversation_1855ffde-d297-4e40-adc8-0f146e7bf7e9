"""
知识图谱引擎模块
提供基于Neo4j的知识图谱查询功能，支持语义向量搜索
"""

from .core.engine import GraphEngine

# 创建全局引擎实例
_graph_engine = None

def get_graph_engine():
    """获取图谱引擎实例"""
    global _graph_engine
    if _graph_engine is None:
        _graph_engine = GraphEngine()
    return _graph_engine

def query_causal_paths(precursor: str, max_depth: int = 5, min_confidence: float = 0.75):
    """查询因果路径"""
    engine = get_graph_engine()
    return engine.query_causal_paths(precursor, max_depth, min_confidence)

def query_complete_paths_by_node(node_description: str, max_depth: int = 5, min_confidence: float = 0.75):
    """根据节点查询包含该节点的所有完整路径"""
    engine = get_graph_engine()
    return engine.query_complete_paths_by_node(node_description, max_depth, min_confidence)

def extract_complete_paths_by_node(node_identifier: str, max_depth: int = 5):
    """提取包含指定节点的完整路径（别名方法）"""
    return query_complete_paths_by_node(node_identifier, max_depth)

def get_reasoning_context(evidence_list: list, max_depth: int = 5, min_confidence: float = 0.75):
    """获取推理上下文"""
    engine = get_graph_engine()
    return engine.get_reasoning_context(evidence_list, max_depth, min_confidence)

def semantic_search(query: str, limit: int = 5, min_score: float = 0.75):
    """语义搜索"""
    engine = get_graph_engine()
    return engine.semantic_search(query, limit, min_score)

# 导出工具列表
graph_tools_list = [
    {
        "name": "query_causal_paths",
        "description": "基于语义相似度查询因果路径",
        "function": query_causal_paths
    },
    {
        "name": "get_reasoning_context",
        "description": "基于多个证据获取综合推理上下文",
        "function": get_reasoning_context
    },
    {
        "name": "semantic_search",
        "description": "语义搜索知识图谱节点",
        "function": semantic_search
    }
]

# 导出名称
__all__ = [
    'GraphEngine',
    'query_causal_paths',
    'get_reasoning_context',
    'semantic_search',
    'graph_tools_list'
]

__version__ = '3.0.0'
