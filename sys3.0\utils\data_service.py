"""
统一数据服务模块
提供数据处理、验证和转换的集成功能
"""

import os
import json
import logging
from pathlib import Path, WindowsPath
from typing import Dict, Any, List, Optional, Union, Tuple, TypeVar, Generic, Iterator, Callable
import re
from datetime import datetime
import hashlib
from functools import wraps

from config.config import config_service
from utils.logger import LoggerMixin
from models.constants import TaskStatus
from utils.exceptions import DataException

# 类型变量
T = TypeVar('T')
U = TypeVar('U')

# 自定义JSON序列化器
def datetime_json_serializer(obj):
    """用于JSON序列化的自定义函数，处理datetime对象和Path对象

    Args:
        obj: 需要序列化的对象

    Returns:
        str: 序列化后的字符串
    """
    if isinstance(obj, datetime):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(obj, (Path, WindowsPath)):
        return str(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

class DataService(LoggerMixin):
    """
    统一数据服务类
    整合数据处理、验证、转换和存储功能
    """
    
    def __init__(self):
        """初始化数据服务"""
        super().__init__()
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        # 获取配置中的路径 - 使用属性获取完整路径
        project_root = Path(config_service.config.paths.project_root)
        self.data_dir = project_root / config_service.config.paths.data_dir
        self.raw_data_dir = config_service.config.paths.raw_data_dir
        self.structured_data_dir = config_service.config.paths.structured_data_dir
        self.evaluation_dir = config_service.config.paths.evaluation_dir
        self.reports_dir = config_service.config.paths.reports_dir
        self.status_dir = config_service.config.paths.status_dir
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.raw_data_dir, exist_ok=True)
        os.makedirs(self.structured_data_dir, exist_ok=True)
        os.makedirs(self.evaluation_dir, exist_ok=True)
        os.makedirs(self.reports_dir, exist_ok=True)
        os.makedirs(self.status_dir, exist_ok=True)
    
    # ================ 任务数据管理功能 ================
    
    def get_task_path(self, task_id: str, phase: str) -> Path:
        """
        获取任务数据路径
        
        Args:
            task_id: 任务ID
            phase: 阶段名称，可选值：raw, structured, evaluation, reports, status
            
        Returns:
            Path: 任务数据路径
        """
        # 规范化任务ID
        task_id = self._normalize_task_id(task_id)
        
        # 根据阶段选择目录
        if phase == "raw":
            # 原始数据存储在子目录中
            path = self.raw_data_dir / task_id
            os.makedirs(path, exist_ok=True)
            return path / f"{task_id}.json"  # 修改为与任务ID一致的文件名
        elif phase == "structured":
            return self.structured_data_dir / f"{task_id}.json"
        elif phase == "evaluation":
            return self.evaluation_dir / f"{task_id}.json"
        elif phase == "report":
            return self.reports_dir / f"{task_id}.md"
        elif phase == "status":
            return self.status_dir / f"{task_id}.json"
        else:
            raise ValueError(f"无效的阶段名称: {phase}")
    
    def ensure_task_directory(self, task_id: str, phase: str) -> Path:
        """
        确保任务目录存在并返回目录路径
        
        Args:
            task_id: 任务ID
            phase: 阶段名称，可选值：raw, structured, evaluation, reports, status
            
        Returns:
            Path: 任务目录路径
        """
        # 规范化任务ID
        task_id = self._normalize_task_id(task_id)
        
        # 根据阶段选择目录
        if phase == "raw":
            # 原始数据存储在子目录中
            path = self.raw_data_dir / task_id
        elif phase == "structured":
            path = self.structured_data_dir
        elif phase == "evaluation":
            path = self.evaluation_dir
        elif phase == "report":
            path = self.reports_dir
        elif phase == "status":
            path = self.status_dir
        else:
            raise ValueError(f"无效的阶段名称: {phase}")
        
        # 确保目录存在
        os.makedirs(path, exist_ok=True)
        return path
    
    def ensure_subdirectory(self, base_dir: Union[str, Path], *subdirs) -> Path:
        """
        确保子目录存在并返回完整路径
        
        Args:
            base_dir: 基础目录
            *subdirs: 子目录名称列表
            
        Returns:
            Path: 完整目录路径
        """
        path = Path(base_dir)
        for subdir in subdirs:
            path = path / subdir
        
        os.makedirs(path, exist_ok=True)
        return path
    
    def save_task_data(self, task_id: str, phase: str, data: Any) -> Path:
        """
        保存任务数据

        Args:
            task_id: 任务ID
            phase: 阶段名称
            data: 要保存的数据

        Returns:
            Path: 保存的文件路径
        """
        try:
            file_path = self.get_task_path(task_id, phase)

            # 确保父目录存在
            os.makedirs(file_path.parent, exist_ok=True)

            # 根据数据类型和阶段选择保存方式
            if phase == "report" and isinstance(data, dict):
                # 报告阶段特殊处理：只保存Markdown文件
                if "report_markdown" in data:
                    # 只保存Markdown文件
                    self.save_text(data["report_markdown"], file_path)
                else:
                    # 如果没有markdown内容，保存为文本文件
                    self.save_text(str(data), file_path)
            elif isinstance(data, (dict, list)):
                self.save_json(data, file_path)
            elif isinstance(data, str):
                self.save_text(data, file_path)
            else:
                raise TypeError(f"不支持的数据类型: {type(data)}")

            self.log_info(f"已保存任务 {task_id} 的 {phase} 阶段数据到 {file_path}")
            return file_path

        except Exception as e:
            error_msg = f"保存任务数据失败 - 任务ID: {task_id}, 阶段: {phase}, 错误: {str(e)}"
            self.log_error(error_msg)
            raise DataException(error_msg)
    
    def load_task_data(self, task_id: str, phase: str) -> Any:
        """
        加载任务数据
        
        Args:
            task_id: 任务ID
            phase: 阶段名称
            
        Returns:
            Any: 加载的数据
        """
        file_path = self.get_task_path(task_id, phase)
        
        # 检查文件是否存在
        if not file_path.exists():
            self.log_error(f"文件不存在: {file_path}")
            raise DataException(f"文件不存在: {file_path}")
        
        # 根据文件扩展名选择加载方式
        suffix = file_path.suffix.lower()
        if suffix == ".json":
            return self.load_json(file_path)
        elif suffix in [".md", ".txt"]:
            return self.load_text(file_path)
        else:
            self.log_warning(f"未知的文件类型: {suffix}")
            return None
    
    def task_data_exists(self, task_id: str, phase: str) -> bool:
        """
        检查任务数据是否存在
        
        Args:
            task_id: 任务ID
            phase: 阶段名称
            
        Returns:
            bool: 数据是否存在
        """
        file_path = self.get_task_path(task_id, phase)
        return file_path.exists()
    
    def update_task_status(self, task_id: str, phase: str, status: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        更新任务状态

        Args:
            task_id: 任务ID
            phase: 阶段名称
            status: 状态值
            details: 状态详情
        """
        status_file = self.get_task_path(task_id, "status")

        # 读取现有状态
        task_status: Dict[str, Any]
        if status_file.exists():
            try:
                task_status = self.load_json(status_file)
            except Exception as e:
                self.log_error(f"读取任务状态失败: {str(e)}")
                task_status = {"task_id": task_id}
        else:
            task_status = {"task_id": task_id}
        
        # 更新状态
        if "phases" not in task_status:
            task_status["phases"] = {}
            
        if phase not in task_status["phases"]:
            task_status["phases"][phase] = {}
            
        task_status["phases"][phase]["status"] = status
        task_status["phases"][phase]["update_time"] = datetime.now().isoformat()
        
        if details:
            task_status["phases"][phase]["details"] = details
            
        # 更新总体状态
        task_status["status"] = self._calculate_overall_status(task_status["phases"])
        task_status["update_time"] = datetime.now().isoformat()
        
        # 保存状态 - 使用自定义JSON序列化器
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(task_status, f, default=datetime_json_serializer, ensure_ascii=False, indent=2)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务状态
        """
        status_file = self.get_task_path(task_id, "status")
        
        if not status_file.exists():
            return {"task_id": task_id, "status": "unknown"}
            
        try:
            return self.load_json(status_file)
        except Exception as e:
            self.log_error(f"读取任务状态失败: {str(e)}")
            return {"task_id": task_id, "status": "error", "error": str(e)}
    
    def _calculate_overall_status(self, phases: Dict[str, Dict[str, Any]]) -> str:
        """
        计算总体任务状态
        
        Args:
            phases: 各阶段状态
            
        Returns:
            str: 总体状态
        """
        # 如果有任何阶段失败，则任务失败
        if any(phase["status"] == TaskStatus.FAILED for phase in phases.values()):
            return TaskStatus.FAILED
            
        # 如果有任何阶段正在运行，则任务正在运行
        if any(phase["status"] == TaskStatus.RUNNING for phase in phases.values()):
            return TaskStatus.RUNNING
            
        # 如果所有阶段都完成，则任务完成
        if all(phase["status"] == TaskStatus.COMPLETED for phase in phases.values() if phase):
            return TaskStatus.COMPLETED
            
        # 默认为创建状态
        return TaskStatus.CREATED
    
    def _normalize_task_id(self, task_id: str) -> str:
        """
        规范化任务ID
        
        Args:
            task_id: 任务ID
            
        Returns:
            str: 规范化后的任务ID
        """
        # 移除添加task_前缀的逻辑，直接返回原始任务ID
        # 如果是旧格式（以task_开头），则移除前缀
        if task_id.startswith("task_"):
            task_id = task_id[5:]
        return task_id
    
    def generate_task_id(self) -> str:
        """
        生成任务ID
        
        Returns:
            str: 任务ID，格式为 TASK-YYYYMMDD-XX
        """
        # 获取当前日期
        today = datetime.now().strftime("%Y%m%d")
        
        # 查找今天已有的任务ID
        pattern = f"TASK-{today}-"
        existing_ids = []
        for file in self.status_dir.glob(f"{pattern}*.*"):
            try:
                id_match = re.search(f"{pattern}(\\d+)", file.stem)
                if id_match:
                    existing_ids.append(int(id_match.group(1)))
            except:
                continue
        
        # 生成新的序号
        next_id = 1
        if existing_ids:
            next_id = max(existing_ids) + 1
        
        # 生成任务ID
        task_id = f"TASK-{today}-{next_id:02d}"
        
        return task_id
    
    # ================ 数据处理功能 ================
    
    def load_json(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> Dict[str, Any]:
        """加载JSON文件"""
        file_path = Path(file_path)
        if not file_path.exists():
            raise DataException(f"文件不存在: {file_path}")
                
        with open(file_path, 'r', encoding=encoding) as f:
            return json.load(f)
    
    def save_json(self, data: Union[Dict[str, Any], List[Any]], file_path: Union[str, Path], encoding: str = 'utf-8') -> None:
        """
        保存JSON数据到文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            encoding: 编码方式
        """
        file_path = Path(file_path)
        
        # 确保目录存在
        os.makedirs(file_path.parent, exist_ok=True)
        
        # 使用自定义JSON序列化器
        with open(file_path, 'w', encoding=encoding) as f:
            json.dump(data, f, default=datetime_json_serializer, ensure_ascii=False, indent=2)
    
    def extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """从文本中提取JSON数据"""
        if not text:
            return None
            
        try:
            # 尝试直接解析整个响应
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                pass
            
            # 尝试从代码块中提取JSON
            json_pattern = r'```(?:json)?\s*([\s\S]*?)\s*```'
            matches = re.findall(json_pattern, text)

            if matches:
                for match in matches:
                    try:
                        # 清理可能的多余空白字符
                        clean_match = match.strip()
                        return json.loads(clean_match)
                    except json.JSONDecodeError:
                        continue
            
            # 尝试提取{开头}结尾的部分
            json_pattern = r'(\{[\s\S]*\})'
            matches = re.findall(json_pattern, text)
            
            if matches:
                for match in matches:
                    try:
                        return json.loads(match)
                    except json.JSONDecodeError:
                        # 尝试清理格式
                        clean_json = re.sub(r'\\n', ' ', match)
                        clean_json = re.sub(r'\\', '', clean_json)
                        try:
                            return json.loads(clean_json)
                        except:
                            continue
            
            # 尝试提取[开头]结尾的部分（数组）
            json_pattern = r'(\[[\s\S]*\])'
            matches = re.findall(json_pattern, text)
            
            if matches:
                for match in matches:
                    try:
                        return json.loads(match)
                    except json.JSONDecodeError:
                        continue
                        
            self.log_warning("无法从文本中提取JSON")
            return None
            
        except Exception as e:
            self.log_error(f"提取JSON时发生错误: {str(e)}")
            return None
    
    def load_text(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """加载文本文件"""
        file_path = Path(file_path)
        if not file_path.exists():
            raise DataException(f"文件不存在: {file_path}")
                
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    
    def save_text(self, text: str, file_path: Union[str, Path], encoding: str = 'utf-8') -> None:
        """保存文本文件"""
        file_path = Path(file_path)
        
        # 确保目录存在
        os.makedirs(file_path.parent, exist_ok=True)
        
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(text)
    
    # ================ 数据验证功能 ================
    
    def validate_type(self, data: Any, expected_type: type, context: str = "数据") -> Tuple[bool, Optional[str]]:
        """验证数据类型"""
        if not isinstance(data, expected_type):
            error_msg = f"{context}类型错误: 期望 {expected_type.__name__}, 实际 {type(data).__name__}"
            return False, error_msg
        return True, None
    
    def validate_required_fields(self, data: Dict[str, Any], required_fields: List[str],
                               context: str = "数据") -> Tuple[bool, List[str]]:
        """验证必要字段"""
        if not isinstance(data, dict):
            return False, [f"{context}不是字典类型"]
            
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            return False, missing_fields
            
        return True, []
    
    def validate_string_length(self, text: str, min_length: int = 0, max_length: Optional[int] = None, 
                             context: str = "字符串") -> Tuple[bool, Optional[str]]:
        """验证字符串长度"""
        if not isinstance(text, str):
            return False, f"{context}不是字符串类型"
            
        if len(text) < min_length:
            return False, f"{context}长度不足 {min_length} 个字符"
            
        if max_length is not None and len(text) > max_length:
            return False, f"{context}超过 {max_length} 个字符"
            
        return True, None
    
    def validate_numeric_range(self, value: Union[int, float], min_value: Optional[Union[int, float]] = None, 
                             max_value: Optional[Union[int, float]] = None, 
                             context: str = "数值") -> Tuple[bool, Optional[str]]:
        """验证数值范围"""
        if not isinstance(value, (int, float)):
            return False, f"{context}不是数值类型"
            
        if min_value is not None and value < min_value:
            return False, f"{context}小于最小值 {min_value}"
            
        if max_value is not None and value > max_value:
            return False, f"{context}大于最大值 {max_value}"
            
        return True, None
    
    def validate_enum(self, value: Any, valid_values: List[Any], context: str = "值") -> Tuple[bool, Optional[str]]:
        """验证枚举值"""
        if value not in valid_values:
            return False, f"{context}不在有效值列表中: {valid_values}"
            
        return True, None
    
    def validate_path(self, path: Union[str, Path], should_exist: bool = True, 
                    is_file: bool = False, is_dir: bool = False,
                    context: str = "路径") -> Tuple[bool, Optional[str]]:
        """验证文件路径"""
        path_obj = Path(path) if isinstance(path, str) else path
        
        if should_exist and not path_obj.exists():
            return False, f"{context}不存在: {path_obj}"
            
        if is_file and not path_obj.is_file():
            return False, f"{context}不是文件: {path_obj}"
            
        if is_dir and not path_obj.is_dir():
            return False, f"{context}不是目录: {path_obj}"
            
        return True, None
    
    def validate_url(self, url: str, context: str = "URL") -> Tuple[bool, Optional[str]]:
        """验证URL格式"""
        url_pattern = re.compile(
            r'^(?:http|ftp)s?://'  # http://, https://, ftp://, ftps://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # domain
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
            
        if not isinstance(url, str):
            return False, f"{context}不是字符串类型"
            
        if not url_pattern.match(url):
            return False, f"{context}格式无效: {url}"
            
        return True, None
    
    def validate_email(self, email: str, context: str = "电子邮件") -> Tuple[bool, Optional[str]]:
        """验证电子邮件格式"""
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
        if not isinstance(email, str):
            return False, f"{context}不是字符串类型"
            
        if not email_pattern.match(email):
            return False, f"{context}格式无效: {email}"
            
        return True, None
    
    def validate_date_format(self, date_str: str, format_str: str = "%Y-%m-%d", 
                           context: str = "日期") -> Tuple[bool, Optional[str]]:
        """验证日期格式"""
        if not isinstance(date_str, str):
            return False, f"{context}不是字符串类型"
            
        try:
            datetime.strptime(date_str, format_str)
            return True, None
        except ValueError:
            return False, f"{context}格式无效，应为 {format_str}: {date_str}"
    
    def validate_api_key(self, api_key: str, provider: Optional[str] = None, context: str = "API密钥") -> Tuple[bool, Optional[str]]:
        """验证API密钥格式"""
        if not isinstance(api_key, str):
            return False, f"{context}不是字符串类型"
            
        if not api_key:
            return False, f"{context}不能为空"
            
        # 针对不同提供商的特定验证
        if provider == "openai" and not api_key.startswith("sk-"):
            return False, f"{context}格式无效，OpenAI API密钥应以'sk-'开头"
            
        return True, None
    
    def validate_data_structure(self, data: Any, required_type: type, phase: str,
                              required_fields: Optional[List[str]] = None) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        验证数据结构
        
        Args:
            data: 要验证的数据
            required_type: 要求的数据类型
            phase: 当前阶段名称
            required_fields: 必须包含的字段列表
            
        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: (验证是否通过, 错误结果)
        """
        # 验证数据类型
        is_valid_type, type_error = self.validate_type(data, required_type, f"{phase}阶段数据")
        if not is_valid_type:
            error_result = {
                "status": "error",
                "error": f"{phase}阶段数据验证失败",
                "details": {
                    "errors": [type_error],
                    "data_type": str(type(data)),
                    "phase": phase
                }
            }
            return False, error_result
        
        # 如果有必要字段要求，验证字段是否存在
        if required_fields and isinstance(data, dict):
            fields_valid, missing_fields = self.validate_required_fields(
                data, required_fields, f"{phase}阶段数据"
            )
            
            if not fields_valid:
                error_result = {
                    "status": "error",
                    "error": f"{phase}阶段数据验证失败",
                    "details": {
                        "errors": [f"缺少必要字段: {', '.join(missing_fields)}"],
                        "missing_fields": missing_fields,
                        "phase": phase
                    }
                }
                return False, error_result
        
        return True, None
    
    # ================ 数据转换功能 ================
    
    def normalize_text(self, text: str) -> str:
        """规范化文本"""
        if not text:
            return ""
        
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text)
        # 去除首尾空白
        text = text.strip()
        
        return text
    
    def extract_date(self, text: str) -> Optional[str]:
        """从文本中提取日期"""
        if not text:
            return None
            
        # 常见日期格式: 2023-01-01, 2023/01/01, 2023年01月01日
        date_patterns = [
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}-\d{1,2}-\d{4})'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                return matches[0]
                
        return None
    
    def generate_id(self, prefix: str = "", data: Any = None) -> str:
        """生成唯一ID"""
        timestamp = int(datetime.now().timestamp())
        
        if data:
            # 使用数据内容生成哈希
            data_str = str(data)
            hash_obj = hashlib.md5(data_str.encode())
            hash_str = hash_obj.hexdigest()[:8]
            return f"{prefix}{timestamp}_{hash_str}"
        else:
            return f"{prefix}{timestamp}"
    
    # ================ 数据管道功能 ================
    
    class DataPipeline(Generic[T, U]):
        """数据处理管道"""
        
        def __init__(self, name: str = "data_pipeline", logger: Optional[logging.Logger] = None):
            """初始化数据管道"""
            self.name = name
            self.steps = []
            self.logger = logger or logging.getLogger(name)
        
        def add_step(self, step: Callable[[Any], Any], name: Optional[str] = None) -> 'DataService.DataPipeline':
            """添加处理步骤"""
            step_name = name or f"step_{len(self.steps) + 1}"
            
            # 包装步骤函数，添加日志和错误处理
            @wraps(step)
            def wrapped_step(data):
                try:
                    self.logger.debug(f"执行步骤 '{step_name}'")
                    result = step(data)
                    return result
                except Exception as e:
                    self.logger.error(f"步骤 '{step_name}' 执行失败: {str(e)}")
                    raise
            
            self.steps.append((step_name, wrapped_step))
            return self
        
        def process(self, data: T) -> U:
            """处理单个数据项"""
            current_data: Any = data

            for step_name, step_func in self.steps:
                try:
                    current_data = step_func(current_data)
                except Exception as e:
                    self.logger.error(f"数据处理失败，步骤 '{step_name}': {str(e)}")
                    raise

            return current_data  # type: ignore
        
        def process_batch(self, data_batch: List[T]) -> List[U]:
            """批量处理数据"""
            results = []
            errors = []
            
            for i, item in enumerate(data_batch):
                try:
                    result = self.process(item)
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"批处理项 {i} 失败: {str(e)}")
                    errors.append((i, str(e)))
            
            if errors:
                self.logger.warning(f"批处理完成，但有 {len(errors)} 个错误")
                
            return results
        
        def process_stream(self, data_stream: Iterator[T]) -> Iterator[U]:
            """流式处理数据"""
            for item in data_stream:
                try:
                    yield self.process(item)
                except Exception as e:
                    self.logger.error(f"流处理项失败: {str(e)}")
                    # 继续处理下一项

# 创建全局单例实例
data_service = DataService()

# 导出名称
__all__ = ['DataService', 'data_service'] 