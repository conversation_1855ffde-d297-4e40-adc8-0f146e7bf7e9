"""
微博搜索爬虫

用于按关键词搜索微博的爬虫
"""

import os
import re
import sys
import time
import json
import threading
from datetime import datetime, timedelta
from urllib.parse import unquote
from pathlib import Path

import requests
import scrapy
from scrapy.exceptions import CloseSpider
from scrapy.utils.project import get_project_settings

from ..items import ScrapyWeiboItem
from ..utils import util

# 导入进度条工具
try:
    from utils.progress import create_progress_bar
except ImportError:
    # 如果导入失败，创建一个简单的替代实现
    def create_progress_bar(total, stage_name, logger=None, **kwargs):
        class DummyProgressBar:
            def update(self, *args, **kwargs): pass
            def finish(self): pass
            def set_total(self, total): pass
        return DummyProgressBar()

class SearchSpider(scrapy.Spider):
    """微博搜索爬虫
    
    用于按关键词搜索微博
    """
    name = 'search'
    allowed_domains = ['weibo.com']
    settings = get_project_settings()
    keyword_list = settings.get('KEYWORD_LIST')
    
    # 添加对keyword_list为None的检查
    if keyword_list is None:
        keyword_list = []
    elif not isinstance(keyword_list, list):
        if not os.path.isabs(keyword_list):
            keyword_list = os.getcwd() + os.sep + keyword_list
        if not os.path.isfile(keyword_list):
            sys.exit('不存在%s文件' % keyword_list)
        keyword_list = util.get_keyword_list(keyword_list)

    for i, keyword in enumerate(keyword_list):
        if len(keyword) > 2 and keyword[0] == '#' and keyword[-1] == '#':
            keyword_list[i] = '%23' + keyword[1:-1] + '%23'
    weibo_type = util.convert_weibo_type(settings.get('WEIBO_TYPE'))
    contain_type = util.convert_contain_type(settings.get('CONTAIN_TYPE'))
    regions = util.get_regions(settings.get('REGION'))
    base_url = 'https://s.weibo.com'
    # 注意：start_date, end_date 和 further_threshold 将在 __init__ 方法中从settings读取
    # 这里不再在类级别读取，避免模块导入时settings未正确加载的问题
    further_threshold = settings.get('FURTHER_THRESHOLD', 46)
    mongo_error = False
    pymongo_error = False
    mysql_error = False
    pymysql_error = False
    
    def __init__(self, *args, **kwargs):
        """初始化微博搜索爬虫

        Args:
            task_id: 任务ID，用于标识本次爬虫任务，默认为当前时间戳
            status_dir: 状态保存目录

        注意：不再接受覆盖settings.py中配置的参数，将仅使用settings中的配置
        """
        super().__init__(*args, **kwargs)

        # 任务ID，用于标识本次爬虫任务
        self.task_id = kwargs.get('task_id', f"task_{int(time.time())}")

        # 状态保存目录 - 只从参数获取，不使用环境变量
        self.status_dir = kwargs.get('status_dir', os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'logs', 'status'))
        # 确保目录存在
        if not os.path.exists(self.status_dir):
            os.makedirs(self.status_dir, exist_ok=True)

        # 在运行时重新读取settings，确保能获取到正确的配置
        # 这样可以避免类级别变量在模块导入时settings未正确加载的问题
        runtime_settings = get_project_settings()
        self.start_date = runtime_settings.get('START_DATE', datetime.now().strftime('%Y-%m-%d'))
        self.end_date = runtime_settings.get('END_DATE', datetime.now().strftime('%Y-%m-%d'))

        # 设置实例级别的配置变量
        self.keyword_list = self.__class__.keyword_list
        self.weibo_type = self.__class__.weibo_type
        self.contain_type = self.__class__.contain_type
        self.regions = self.__class__.regions

        # 验证日期配置
        if util.str_to_time(self.start_date) > util.str_to_time(self.end_date):
            self.logger.error('配置错误，START_DATE值应早于或等于END_DATE值，请重新配置')
            sys.exit('配置错误，START_DATE值应早于或等于END_DATE值，请重新配置')

        # 初始化状态
        self.status = "running"
        self.save_status({
            "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "progress": 0
        })

        # 初始化计数器
        self.total_requests = 0
        self.completed_requests = 0

        # 初始化进度条（将在start_requests中设置总数）
        self.progress_bar = None

        # 初始化超时监控机制
        self.timeout_seconds = 300  # 5分钟超时
        self.last_item_count = 0
        self.last_item_time = time.time()
        self.timeout_timer = None
        self.timeout_check_interval = 60  # 每60秒检查一次
        self.is_closing = False

        # 初始化日志
        self.logger.info(f"微博搜索爬虫已初始化，任务ID: {self.task_id}")
        self.logger.info(f"关键词列表: {self.keyword_list}")
        self.logger.info(f"时间范围: {self.start_date} - {self.end_date}")
        self.logger.info(f"地区: {list(self.regions.keys())}")
        self.logger.info(f"开始生成搜索请求，关键词数量: {len(self.keyword_list)}, 地区数量: {len(self.regions)}")
        self.logger.info(f"超时监控已启用，超时时间: {self.timeout_seconds}秒")
    
    def save_status(self, data):
        """保存爬虫状态

        Args:
            data: 状态数据
        """
        util.save_task_status(self.task_id, self.status, data, self.status_dir)

    def start_timeout_monitor(self):
        """启动超时监控

        独立的定时器，定期检查数据收集是否停滞
        """
        if self.is_closing:
            return

        def check_timeout():
            if self.is_closing:
                return

            try:
                # 获取当前的item数量
                current_item_count = self.crawler.stats.get_value('item_scraped_count', 0)
                current_time = time.time()

                # 检查数据是否有增长
                if current_item_count > self.last_item_count:
                    # 有新数据，更新记录
                    self.last_item_count = current_item_count
                    self.last_item_time = current_time
                    self.logger.debug(f"数据更新检查: 当前收集 {current_item_count} 条微博")
                else:
                    # 没有新数据，检查是否超时
                    time_since_last_item = current_time - self.last_item_time
                    if time_since_last_item > self.timeout_seconds:
                        self.logger.warning(f"检测到 {time_since_last_item:.0f} 秒无新数据增长，触发超时机制")
                        self.logger.warning(f"当前微博数量: {current_item_count} 条，最后更新时间: {datetime.fromtimestamp(self.last_item_time).strftime('%Y-%m-%d %H:%M:%S')}")

                        # 触发爬虫关闭
                        self.is_closing = True
                        if hasattr(self.crawler, 'engine'):
                            self.crawler.engine.close_spider(self, 'timeout_no_new_data')
                        return
                    else:
                        remaining_time = self.timeout_seconds - time_since_last_item
                        self.logger.debug(f"超时检查: 距离上次数据更新 {time_since_last_item:.0f} 秒，剩余 {remaining_time:.0f} 秒")

                # 继续监控
                if not self.is_closing:
                    self.timeout_timer = threading.Timer(self.timeout_check_interval, check_timeout)
                    self.timeout_timer.daemon = True
                    self.timeout_timer.start()

            except Exception as e:
                self.logger.error(f"超时检查过程中发生错误: {e}")
                # 即使出错也要继续监控
                if not self.is_closing:
                    self.timeout_timer = threading.Timer(self.timeout_check_interval, check_timeout)
                    self.timeout_timer.daemon = True
                    self.timeout_timer.start()

        # 启动第一次检查
        self.timeout_timer = threading.Timer(self.timeout_check_interval, check_timeout)
        self.timeout_timer.daemon = True
        self.timeout_timer.start()
        self.logger.info(f"超时监控已启动，检查间隔: {self.timeout_check_interval}秒")

    def stop_timeout_monitor(self):
        """停止超时监控"""
        self.is_closing = True
        if self.timeout_timer:
            self.timeout_timer.cancel()
            self.logger.info("超时监控已停止")
    
    def check_environment(self):
        """判断配置要求的软件是否已安装"""
        if self.pymongo_error:
            self.logger.error('系统中可能没有安装pymongo库，请先运行 pip install pymongo ，再运行程序')
            raise CloseSpider('未安装pymongo库')
        if self.mongo_error:
            self.logger.error('系统中可能没有安装或启动MongoDB数据库，请先根据系统环境安装或启动MongoDB，再运行程序')
            raise CloseSpider('MongoDB连接失败')
        if self.pymysql_error:
            self.logger.error('系统中可能没有安装pymysql库，请先运行 pip install pymysql ，再运行程序')
            raise CloseSpider('未安装pymysql库')
        if self.mysql_error:
            self.logger.error('系统中可能没有安装或正确配置MySQL数据库，请先根据系统环境安装或配置MySQL，再运行程序')
            raise CloseSpider('MySQL连接失败')
    
    def start_requests(self):
        """生成起始请求

        对每个关键词，生成对应的搜索请求

        Returns:
            Request: 搜索请求
        """
        self.logger.info(f"开始生成搜索请求，关键词列表: {self.keyword_list}")

        # 初始化请求计数器，用于进度跟踪
        self.total_requests = 0
        self.completed_requests = 0

        start_date = datetime.strptime(self.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(self.end_date, '%Y-%m-%d') + timedelta(days=1)
        start_str = start_date.strftime('%Y-%m-%d') + '-0'
        end_str = end_date.strftime('%Y-%m-%d') + '-0'

        for keyword in self.keyword_list:
            if not self.settings.get('REGION') or '全部' in self.settings.get('REGION'):
                base_url = 'https://s.weibo.com/weibo?q=%s' % keyword
                url = base_url + self.weibo_type
                url += self.contain_type
                url += '&timescope=custom:{}:{}'.format(start_str, end_str)
                self.logger.debug(f"生成搜索请求: {url}")
                self.total_requests += 1
                yield scrapy.Request(url=url,
                                   callback=self.parse,
                                   meta={
                                       'base_url': base_url,
                                       'keyword': keyword
                                   },
                                   errback=self.handle_error)
            else:
                for region in self.regions.values():
                    base_url = (
                        'https://s.weibo.com/weibo?q={}&region=custom:{}:1000'
                    ).format(keyword, region['code'])
                    url = base_url + self.weibo_type
                    url += self.contain_type
                    url += '&timescope=custom:{}:{}'.format(start_str, end_str)
                    self.logger.debug(f"生成搜索请求: {url}, 地区: {region.get('name')}")
                    self.total_requests += 1
                    # 获取一个省的搜索结果
                    yield scrapy.Request(url=url,
                                       callback=self.parse,
                                       meta={
                                           'base_url': base_url,
                                           'keyword': keyword,
                                           'province': region
                                       },
                                       errback=self.handle_error)
        
        # 保存状态
        self.save_status({
            "total_requests": self.total_requests,
            "completed_requests": 0,
            "progress": 0
        })

        # 初始化进度条
        if self.total_requests > 0:
            self.progress_bar = create_progress_bar(
                total=self.total_requests,
                stage_name="信息搜集",
                logger=self.logger,
                update_threshold=5  # 每5%显示一次进度
            )
            self.logger.info(f"开始执行 {self.total_requests} 个搜索请求")

        # 启动超时监控
        self.start_timeout_monitor()
    
    def handle_error(self, failure):
        """处理请求错误
        
        Args:
            failure: 错误信息
        """
        request = failure.request
        self.logger.error(f"请求失败: {failure.value}")
        
        # 更新进度
        self.completed_requests += 1
        self.update_progress()
        
        # 保存错误信息
        self.save_status({
            "error": f"请求失败: {failure.value}",
            "request": {
                "url": request.url,
                "meta": request.meta.get('request_info', {})
            }
        })
    
    def parse(self, response):
        """解析搜索结果页面
        
        根据搜索结果页面的情况，决定是直接解析，还是进一步细分搜索条件
        
        Args:
            response: 响应对象
            
        Returns:
            Item或Request: 微博数据或进一步的请求
        """
        base_url = response.meta.get('base_url')
        keyword = response.meta.get('keyword')
        province = response.meta.get('province')
        
        # 更新完成请求计数
        self.completed_requests += 1
        self.update_progress()
        
        # 检查是否为空结果页面
        is_empty = response.xpath('//div[@class="card card-no-result s-pt20b40"]')
        page_count = len(response.xpath('//ul[@class="s-scroll"]/li'))
        
        # 记录请求信息
        self.save_status({
            "url": response.url,
            "keyword": keyword,
            "is_empty": bool(is_empty),
            "page_count": page_count,
            "province": province.get("name") if province else "全部"
        })
        
        self.logger.debug(f"解析搜索页面: {response.url}, 关键词: {keyword}, 页数: {page_count}, 是否为空: {bool(is_empty)}")
        
        if is_empty:
            self.logger.info(f'关键词 "{keyword}" 搜索结果为空')
        elif page_count < self.further_threshold:
            # 结果页数少于阈值，直接解析当前页面
            self.logger.info(f'关键词 "{keyword}" 搜索结果页数 {page_count} < {self.further_threshold}，直接解析')
            
            # 解析当前页面
            item_count = 0
            for weibo in self.parse_weibo(response):
                item_count += 1
                self.check_environment()
                yield weibo
                
            self.logger.debug(f'页面 "{response.url}" 解析完成，提取了 {item_count} 条微博')
            
            # 如果有下一页，继续抓取下一页
            next_url = response.xpath('//a[@class="next"]/@href').extract_first()
            if next_url:
                base_url = response.meta.get('base_url', self.base_url)
                next_url = base_url + next_url
                self.logger.debug(f'抓取下一页 {next_url}')
                self.total_requests += 1
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_page,
                    meta={'keyword': keyword},
                    errback=self.handle_error
                )
            else:
                self.logger.info(f'关键词 "{keyword}" 没有下一页')
        else:
            # 结果页数太多，按天细分搜索
            self.logger.info(f'关键词 "{keyword}" 搜索结果页数 {page_count} >= {self.further_threshold}，按天细分搜索')
            
            start_date = datetime.strptime(self.start_date, '%Y-%m-%d')
            end_date = datetime.strptime(self.end_date, '%Y-%m-%d')
            while start_date <= end_date:
                start_str = start_date.strftime('%Y-%m-%d') + '-0'
                start_date = start_date + timedelta(days=1)
                end_str = start_date.strftime('%Y-%m-%d') + '-0'
                url = base_url + self.weibo_type
                url += self.contain_type
                url += '&timescope=custom:{}:{}&page=1'.format(
                    start_str, end_str)
                # 获取一天的搜索结果
                self.logger.info(f'按天细分搜索: {start_str[:-2]}')
                self.total_requests += 1
                yield scrapy.Request(url=url,
                                     callback=self.parse_by_day,
                                     meta={
                                         'base_url': base_url,
                                         'keyword': keyword,
                                         'province': province,
                                         'date': start_str[:-2]
                                     },
                                     errback=self.handle_error)
    
    def parse_by_day(self, response):
        """按天解析搜索结果
        
        根据一天内的搜索结果数量决定是否需要按小时进一步细分
        
        Args:
            response: 响应对象
            
        Returns:
            Item或Request: 微博数据或进一步的请求
        """
        base_url = response.meta.get('base_url')
        keyword = response.meta.get('keyword')
        province = response.meta.get('province')
        date = response.meta.get('date')
        
        # 检查是否为空结果页面
        is_empty = response.xpath('//div[@class="card card-no-result s-pt20b40"]')
        page_count = len(response.xpath('//ul[@class="s-scroll"]/li'))
        
        if is_empty:
            self.logger.debug(f'{date} 当前页面搜索结果为空')
        elif page_count < self.further_threshold:
            # 结果页数少于阈值，直接解析当前页面
            self.logger.debug(f'{date} 搜索结果页数 {page_count} < {self.further_threshold}，直接解析')
            
            # 解析当前页面
            for weibo in self.parse_weibo(response):
                self.check_environment()
                yield weibo
            
            # 如果有下一页，继续抓取下一页
            next_url = response.xpath('//a[@class="next"]/@href').extract_first()
            if next_url:
                base_url = response.meta.get('base_url', self.base_url)
                next_url = base_url + next_url
                self.logger.debug(f'抓取下一页 {next_url}')
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_page,
                    meta={'keyword': keyword}
                )
        else:
            # 结果页数太多，按小时细分搜索
            self.logger.debug(f'{date} 搜索结果页数 {page_count} >= {self.further_threshold}，按小时细分搜索')
            
            # 将日期字符串转换为datetime对象
            start_date_str = date + '-0'
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d-%H')
            
            # 按小时生成4个小时的搜索请求
            for i in range(1, 25):
                # 构造每小时的时间范围
                hour_start_str = start_date.strftime('%Y-%m-%d-X%H').replace('X0', 'X').replace('X', '')
                start_date = start_date + timedelta(hours=1)
                hour_end_str = start_date.strftime('%Y-%m-%d-X%H').replace('X0', 'X').replace('X', '')
                
                # 构建URL
                url = base_url + self.weibo_type
                url += self.contain_type
                url += f'&timescope=custom:{hour_start_str}:{hour_end_str}&page=1'
                
                # 确定回调函数，根据是否需要按地区和小时双重细分
                # 如果当前已经是在用省份筛选，则继续按省份和小时筛选
                # 否则先按小时筛选，如果还不够，再按小时+地区筛选
                callback_func = self.parse_by_hour_province if province else self.parse_by_hour
                
                # 获取一小时的一个省的搜索结果
                self.logger.debug(f'按小时细分搜索 {hour_start_str} - {hour_end_str}')
                yield scrapy.Request(
                    url=url,
                    callback=callback_func,
                    meta={
                        'base_url': base_url,
                        'keyword': keyword,
                        'province': province,
                        'start_time': hour_start_str,
                        'end_time': hour_end_str
                    }
                )
    
    def parse_by_hour(self, response):
        """按小时解析搜索结果
        
        根据一小时内的搜索结果数量决定是否需要按地区进一步细分
        
        Args:
            response: 响应对象
            
        Returns:
            Item或Request: 微博数据或进一步的请求
        """
        keyword = response.meta.get('keyword')
        start_time = response.meta.get('start_time')
        end_time = response.meta.get('end_time')
        
        # 检查是否为空结果页面
        is_empty = response.xpath('//div[@class="card card-no-result s-pt20b40"]')
        page_count = len(response.xpath('//ul[@class="s-scroll"]/li'))
        
        if is_empty:
            self.logger.debug(f'{start_time} 当前页面搜索结果为空')
        elif page_count < self.further_threshold:
            # 结果页数少于阈值，直接解析当前页面
            self.logger.debug(f'{start_time} 搜索结果页数 {page_count} < {self.further_threshold}，直接解析')
            
            # 解析当前页面
            for weibo in self.parse_weibo(response):
                self.check_environment()
                yield weibo
            
            # 如果有下一页，继续抓取下一页
            next_url = response.xpath('//a[@class="next"]/@href').extract_first()
            if next_url:
                base_url = response.meta.get('base_url', self.base_url)
                next_url = base_url + next_url
                self.logger.debug(f'抓取下一页 {next_url}')
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_page,
                    meta={'keyword': keyword}
                )
        else:
            # 结果页数太多，按省份细分搜索
            self.logger.debug(f'{start_time} 搜索结果页数 {page_count} >= {self.further_threshold}，按省份细分搜索')
            
            # 为每个省份生成请求
            for region_name, region in self.regions.items():
                # 构建URL，包含省份代码
                url = f'https://s.weibo.com/weibo?q={keyword}&region=custom:{region["code"]}:1000'
                url += self.weibo_type
                url += self.contain_type
                url += f'&timescope=custom:{start_time}:{end_time}&page=1'
                
                # 获取一小时一个省的搜索结果
                self.logger.debug(f'按小时和省份细分搜索: {start_time} - {end_time}, 省份: {region_name}')
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_by_hour_province,
                    meta={
                        'base_url': self.base_url,
                        'keyword': keyword,
                        'start_time': start_time,
                        'end_time': end_time,
                        'province': region
                    }
                )
    
    def parse_by_hour_province(self, response):
        """按小时和省份解析搜索结果
        
        根据一小时内一个省的搜索结果数量决定是否需要按城市进一步细分
        
        Args:
            response: 响应对象
            
        Returns:
            Item或Request: 微博数据或进一步的请求
        """
        keyword = response.meta.get('keyword')
        start_time = response.meta.get('start_time')
        end_time = response.meta.get('end_time')
        province = response.meta.get('province')
        
        # 检查是否为空结果页面
        is_empty = response.xpath('//div[@class="card card-no-result s-pt20b40"]')
        page_count = len(response.xpath('//ul[@class="s-scroll"]/li'))
        
        if is_empty:
            self.logger.debug(f'{start_time} - 省份 {province["code"]} 搜索结果为空')
        elif page_count < self.further_threshold:
            # 结果页数少于阈值，直接解析当前页面
            self.logger.debug(f'{start_time} - 省份 {province["code"]} 搜索结果页数 {page_count} < {self.further_threshold}，直接解析')
            
            # 解析当前页面
            for weibo in self.parse_weibo(response):
                self.check_environment()
                yield weibo
            
            # 如果有下一页，继续抓取下一页
            next_url = response.xpath('//a[@class="next"]/@href').extract_first()
            if next_url:
                base_url = response.meta.get('base_url', self.base_url)
                next_url = base_url + next_url
                self.logger.debug(f'抓取下一页 {next_url}')
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_page,
                    meta={'keyword': keyword}
                )
        else:
            # 结果页数太多，按城市细分搜索
            self.logger.debug(f'{start_time} - 省份 {province["code"]} 搜索结果页数 {page_count} >= {self.further_threshold}，按城市细分搜索')
            
            # 为每个城市生成请求
            for city_name, city_code in province['city'].items():
                # 构建URL，包含省份和城市代码
                url = f'https://s.weibo.com/weibo?q={keyword}&region=custom:{province["code"]}:{city_code}'
                url += self.weibo_type
                url += self.contain_type
                url += f'&timescope=custom:{start_time}:{end_time}&page=1'
                
                # 获取一小时一个城市的搜索结果
                self.logger.debug(f'按小时、省份和城市细分搜索: {start_time} - {end_time}, 省份: {province["code"]}, 城市: {city_name}({city_code})')
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_page,
                    meta={
                        'keyword': keyword,
                        'start_time': start_time,
                        'end_time': end_time,
                        'province': province,
                        'city': city_code
                    }
                )
    
    def parse_page(self, response):
        """解析一页搜索结果
        
        Args:
            response: 响应对象
            
        Returns:
            Item或Request: 微博数据或下一页请求
        """
        keyword = response.meta.get('keyword')
        
        # 检查是否为空结果页面
        is_empty = response.xpath('//div[@class="card card-no-result s-pt20b40"]')
        
        if is_empty:
            self.logger.debug('当前页面搜索结果为空')
        else:
            # 解析当前页面
            for weibo in self.parse_weibo(response):
                self.check_environment()
                yield weibo
            
            # 如果有下一页，继续抓取下一页
            next_url = response.xpath('//a[@class="next"]/@href').extract_first()
            if next_url:
                base_url = response.meta.get('base_url', self.base_url)
                next_url = base_url + next_url
                self.logger.debug(f'抓取下一页 {next_url}')
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_page,
                    meta={'keyword': keyword}
                )
    


    def get_ip(self, bid):
        """获取微博发布IP
        
        通过微博bid获取发布IP
        
        Args:
            bid: 微博bid
            
        Returns:
            str: IP地址字符串
        """
        url = f"https://weibo.com/ajax/statuses/show?id={bid}&locale=zh-CN"
        try:
            response = requests.get(url, headers=self.settings.get('DEFAULT_REQUEST_HEADERS'))
            if response.status_code != 200:
                return ""
            data = response.json()
            ip_str = data.get("region_name", "")
            if ip_str:
                ip_str = ip_str.split()[-1]
            return ip_str
        except Exception as e:
            self.logger.error(f"获取IP失败: {e}")
            return ""
    
    def get_article_url(self, selector):
        """获取微博头条文章url

        Args:
            selector: 微博内容选择器

        Returns:
            str: 头条文章URL
        """
        try:
            article_url = ''
            text = selector.xpath('string(.)').extract_first()
            if text:
                text = text.replace('\u200b', '').replace('\ue627', '').replace('\n', '').replace(' ', '')
                if text.startswith('发布了头条文章'):
                    urls = selector.xpath('.//a')
                    for url in urls:
                        icon_text = url.xpath('i[@class="wbicon"]/text()').extract_first()
                        if icon_text == 'O':
                            href = url.xpath('@href').extract_first()
                            if href and href.startswith('http://t.cn'):
                                article_url = href
                            break
            return article_url
        except Exception as e:
            self.logger.warning(f"获取头条文章URL失败: {e}")
            return ''

    def get_location(self, selector):
        """获取微博发布位置

        Args:
            selector: 微博内容选择器

        Returns:
            str: 位置字符串
        """
        try:
            a_list = selector.xpath('.//a')
            location = ''
            for a in a_list:
                if a.xpath('./i[@class="wbicon"]'):
                    icon_text = a.xpath('./i[@class="wbicon"]/text()').extract_first()
                    if icon_text == '2':
                        location_text = a.xpath('string(.)').extract_first()
                        if location_text and len(location_text) > 1:
                            location = location_text[1:]
                        break
            return location
        except Exception as e:
            self.logger.warning(f"获取位置信息失败: {e}")
            return ''

    def get_at_users(self, selector):
        """获取微博中@的用户昵称

        Args:
            selector: 微博内容选择器

        Returns:
            str: @用户昵称列表，以逗号分隔
        """
        try:
            a_list = selector.xpath('.//a')
            at_users = ''
            at_list = []
            for a in a_list:
                href = a.xpath('@href').extract_first()
                text = a.xpath('string(.)').extract_first()

                if href and text and len(unquote(href)) > 14 and len(text) > 1:
                    if unquote(href)[14:] == text[1:]:
                        at_user = text[1:]
                        if at_user not in at_list:
                            at_list.append(at_user)
            if at_list:
                at_users = ','.join(at_list)
            return at_users
        except Exception as e:
            self.logger.warning(f"获取@用户失败: {e}")
            return ''

    def get_topics(self, selector):
        """获取参与的微博话题

        Args:
            selector: 微博内容选择器

        Returns:
            str: 话题列表，以逗号分隔
        """
        try:
            a_list = selector.xpath('.//a')
            topics = ''
            topic_list = []
            for a in a_list:
                text = a.xpath('string(.)').extract_first()
                if text and len(text) > 2 and text[0] == '#' and text[-1] == '#':
                    topic = text[1:-1]
                    if topic not in topic_list:
                        topic_list.append(topic)
            if topic_list:
                topics = ','.join(topic_list)
            return topics
        except Exception as e:
            self.logger.warning(f"获取话题失败: {e}")
            return ''
    
    def update_progress(self):
        """更新爬虫进度

        使用统一的进度条显示当前进度
        """
        # 防止除零错误
        if not hasattr(self, 'total_requests') or self.total_requests == 0:
            return

        # 使用进度条更新进度
        if self.progress_bar:
            self.progress_bar.update(current=self.completed_requests)

        # 计算进度百分比用于状态保存
        progress = round(self.completed_requests / self.total_requests * 100, 2)

        # 保存进度状态
        self.save_status({
            "progress": progress,
            "completed_requests": self.completed_requests,
            "total_requests": self.total_requests
        })
    
    def extract_statistics(self, card):
        """提取微博统计数据（转发数、评论数、点赞数）
        
        Args:
            card: 微博卡片元素
            
        Returns:
            tuple: (转发数, 评论数, 点赞数)
        """
        try:
            # 转发数
            reposts = card.xpath('.//a[@action-type="fl_forward"]/text()').extract_first()
            if reposts:
                reposts = reposts.strip()
                reposts = reposts.split(' ')[-1]
                if reposts == '转发':
                    reposts = '0'
                elif '万' in reposts:
                    reposts = float(reposts.replace('万', '')) * 10000
                    reposts = str(int(reposts))
            else:
                reposts = '0'
            
            # 评论数
            comments = card.xpath('.//a[@action-type="fl_comment"]/text()').extract_first()
            if comments:
                comments = comments.strip()
                comments = comments.split(' ')[-1]
                if comments == '评论':
                    comments = '0'
                elif '万' in comments:
                    comments = float(comments.replace('万', '')) * 10000
                    comments = str(int(comments))
            else:
                comments = '0'
            
            # 点赞数
            attitudes = card.xpath('.//a[@action-type="fl_like"]/span/text()').extract_first()
            if attitudes:
                attitudes = attitudes.strip()
                if attitudes == '赞':
                    attitudes = '0'
                elif '万' in attitudes:
                    attitudes = float(attitudes.replace('万', '')) * 10000
                    attitudes = str(int(attitudes))
            else:
                attitudes = '0'
            
            return reposts, comments, attitudes
        except Exception as e:
            self.logger.error(f'提取统计数据出错: {e}')
            return '0', '0', '0'
    
    def extract_user_info(self, card):
        """提取用户信息
        
        Args:
            card: 微博卡片元素
            
        Returns:
            tuple: (用户ID, 用户昵称, 用户认证信息)
        """
        try:
            # 用户ID和昵称
            user_info = card.xpath('.//div[@class="info"]/div[2]')
            if user_info:
                user_a = user_info.xpath('./a[@class="name"]')
                if user_a:
                    user_link = user_a.xpath('./@href').extract_first()
                    user_id = user_link.split('/')[-1]
                    user_name = user_a.xpath('./text()').extract_first().strip()
                else:
                    user_id = ''
                    user_name = ''
            else:
                # 尝试其他位置获取用户信息
                user_link = card.xpath('.//div[@class="content"]/div/a[@class="name"]/@href').extract_first()
                if user_link:
                    user_id = user_link.split('/')[-1]
                    user_name = card.xpath('.//div[@class="content"]/div/a[@class="name"]/text()').extract_first()
                    user_name = user_name.strip() if user_name else ''
                else:
                    user_id = ''
                    user_name = ''
            
            # 用户认证信息
            user_auth = card.xpath('.//div[@class="info"]/div[2]/a/i/@title').extract_first()
            if not user_auth:
                # 尝试其他位置获取认证信息
                user_auth = card.xpath('.//div[@class="content"]/div/a[contains(@href, "/verified")]/i/@title').extract_first()
            
            return user_id, user_name, user_auth or ''
        except Exception as e:
            self.logger.error(f'提取用户信息出错: {e}')
            return '', '', ''
    
    def extract_media(self, card):
        """提取媒体信息（图片和视频）
        
        Args:
            card: 微博卡片元素
            
        Returns:
            tuple: (图片URLs, 视频URL)
        """
        try:
            # 图片URL
            pic_links = []
            pics = card.xpath('.//div[@class="media media-piclist"]')
            if pics:
                for pic in pics.xpath('.//img'):
                    pic_link = pic.xpath('./@src').extract_first()
                    if pic_link:
                        # 将缩略图URL转换为原图URL
                        if pic_link.startswith('http'):
                            pic_links.append(pic_link.replace('orj360', 'large'))
            
            # 视频URL
            video_link = ''
            videos = card.xpath('.//div[@class="thumbnail"]')
            if videos:
                video_a = videos.xpath('./a/@action-data').extract_first()
                if video_a:
                    video_data = {}
                    for i in video_a.split('&'):
                        if i.find('=') > 0:
                            key, value = i.split('=', 1)
                            video_data[key] = value
                    if 'video_src' in video_data:
                        video_link = video_data['video_src']
                        if video_link.startswith('http'):
                            pass
                        else:
                            video_link = ''
            
            return ','.join(pic_links), video_link
        except Exception as e:
            self.logger.error(f'提取媒体信息出错: {e}')
            return '', ''
    
    def parse_weibo(self, response):
        """解析网页中的微博信息"""
        keyword = response.meta.get('keyword')
        for sel in response.xpath("//div[@class='card-wrap']"):
            info = sel.xpath(
                "div[@class='card']/div[@class='card-feed']/div[@class='content']/div[@class='info']"
            )
            if info:
                weibo = ScrapyWeiboItem()
                weibo['id'] = sel.xpath('@mid').extract_first()

                # 安全地提取bid和真实微博URL - 尝试多种方式获取
                bid = ''
                real_weibo_url = ''
                # 方法1: 从时间链接中提取
                bid_href = sel.xpath('.//div[@class="from"]/a[1]/@href').extract_first()
                if bid_href:
                    try:
                        # 记录真实的微博URL，并打印调试信息
                        self.logger.debug(f"获取到的原始链接: {bid_href}")

                        if bid_href.startswith('http'):
                            real_weibo_url = bid_href
                        elif bid_href.startswith('//'):
                            # 处理 //weibo.com/... 格式的相对URL
                            real_weibo_url = 'https:' + bid_href
                        elif bid_href.startswith('/'):
                            real_weibo_url = 'https://weibo.com' + bid_href

                        self.logger.debug(f"处理后的微博URL: {real_weibo_url}")

                        # 提取URL中的最后一部分作为bid
                        bid_part = bid_href.split('/')[-1].split('?')[0]
                        if bid_part and len(bid_part) > 3:  # 确保bid有效
                            bid = bid_part
                    except (IndexError, AttributeError):
                        pass

                # 方法2: 如果没有bid，尝试从其他链接提取
                if not bid:
                    all_links = sel.xpath('.//a/@href').extract()
                    for link in all_links:
                        if '/detail/' in link or '/status/' in link:
                            try:
                                # 如果还没有真实URL，记录这个
                                if not real_weibo_url:
                                    if link.startswith('http'):
                                        real_weibo_url = link
                                    elif link.startswith('//'):
                                        # 处理 //weibo.com/... 格式的相对URL
                                        real_weibo_url = 'https:' + link
                                    elif link.startswith('/'):
                                        real_weibo_url = 'https://weibo.com' + link

                                bid_part = link.split('/')[-1].split('?')[0]
                                if bid_part and len(bid_part) > 3:
                                    bid = bid_part
                                    break
                            except (IndexError, AttributeError):
                                continue

                weibo['bid'] = bid

                # 安全地提取用户ID
                user_href = info[0].xpath('div[2]/a/@href').extract_first() if info else None
                if user_href:
                    try:
                        user_id = user_href.split('?')[0].split('/')[-1]
                    except (IndexError, AttributeError):
                        user_id = ''
                else:
                    user_id = ''
                weibo['user_id'] = user_id

                # 安全地提取用户昵称
                screen_name = info[0].xpath('div[2]/a/@nick-name').extract_first() if info else None
                weibo['screen_name'] = screen_name or ''
                txt_sel = sel.xpath('.//p[@class="txt"]')[0]
                retweet_sel = sel.xpath('.//div[@class="card-comment"]')
                retweet_txt_sel = ''
                if retweet_sel and retweet_sel[0].xpath('.//p[@class="txt"]'):
                    retweet_txt_sel = retweet_sel[0].xpath(
                        './/p[@class="txt"]')[0]
                content_full = sel.xpath(
                    './/p[@node-type="feed_list_content_full"]')
                is_long_weibo = False
                is_long_retweet = False
                if content_full:
                    if not retweet_sel:
                        txt_sel = content_full[0]
                        is_long_weibo = True
                    elif len(content_full) == 2:
                        txt_sel = content_full[0]
                        retweet_txt_sel = content_full[1]
                        is_long_weibo = True
                        is_long_retweet = True
                    elif retweet_sel[0].xpath(
                            './/p[@node-type="feed_list_content_full"]'):
                        retweet_txt_sel = retweet_sel[0].xpath(
                            './/p[@node-type="feed_list_content_full"]')[0]
                        is_long_retweet = True
                    else:
                        txt_sel = content_full[0]
                        is_long_weibo = True
                weibo['text'] = txt_sel.xpath(
                    'string(.)').extract_first().replace('\u200b', '').replace(
                    '\ue627', '')
                weibo['article_url'] = self.get_article_url(txt_sel)
                weibo['location'] = self.get_location(txt_sel)
                if weibo['location']:
                    weibo['text'] = weibo['text'].replace(
                        '2' + weibo['location'], '')
                weibo['text'] = weibo['text'][2:].replace(' ', '')
                if is_long_weibo:
                    weibo['text'] = weibo['text'][:-4]
                weibo['at_users'] = self.get_at_users(txt_sel)
                weibo['topics'] = self.get_topics(txt_sel)
                # 安全地提取转发数
                try:
                    reposts_count = sel.xpath(
                        './/a[@action-type="feed_list_forward"]/text()').extract()
                    reposts_count = "".join(reposts_count) if reposts_count else ''
                    if reposts_count:
                        reposts_count = re.findall(r'\d+.*', reposts_count)
                        weibo['reposts_count'] = reposts_count[0] if reposts_count else '0'
                    else:
                        weibo['reposts_count'] = '0'
                except (TypeError, AttributeError, IndexError) as e:
                    self.logger.warning(f"无法解析转发数: {e}")
                    weibo['reposts_count'] = '0'
                # 安全地提取评论数
                try:
                    comments_count = sel.xpath(
                        './/a[@action-type="feed_list_comment"]/text()'
                    ).extract_first()
                    if comments_count:
                        comments_count = re.findall(r'\d+.*', comments_count)
                        weibo['comments_count'] = comments_count[0] if comments_count else '0'
                    else:
                        weibo['comments_count'] = '0'
                except (TypeError, AttributeError, IndexError) as e:
                    self.logger.warning(f"无法解析评论数: {e}")
                    weibo['comments_count'] = '0'
                # 安全地提取点赞数
                try:
                    attitudes_count = sel.xpath(
                        './/a[@action-type="feed_list_like"]/button/span[2]/text()').extract_first()
                    if attitudes_count:
                        attitudes_count = re.findall(r'\d+.*', attitudes_count)
                        weibo['attitudes_count'] = attitudes_count[0] if attitudes_count else '0'
                    else:
                        weibo['attitudes_count'] = '0'
                except (TypeError, AttributeError, IndexError) as e:
                    self.logger.warning(f"无法解析点赞数: {e}")
                    weibo['attitudes_count'] = '0'
                # 安全地提取创建时间
                try:
                    created_at = sel.xpath(
                        './/div[@class="from"]/a[1]/text()').extract_first()
                    if created_at:
                        created_at = created_at.replace(' ', '').replace('\n', '').split('前')[0]
                        weibo['created_at'] = util.standardize_date(created_at)
                    else:
                        weibo['created_at'] = ''
                except (AttributeError, IndexError) as e:
                    self.logger.warning(f"无法解析创建时间: {e}")
                    weibo['created_at'] = ''
                source = sel.xpath('.//div[@class="from"]/a[2]/text()'
                                   ).extract_first()
                weibo['source'] = source if source else ''
                pics = ''
                is_exist_pic = sel.xpath(
                    './/div[@class="media media-piclist"]')
                if is_exist_pic:
                    pics = is_exist_pic[0].xpath('ul[1]/li/img/@src').extract()
                    pics = [pic[8:] for pic in pics]
                    pics = [
                        re.sub(r'/.*?/', '/large/', pic, 1) for pic in pics
                    ]
                    pics = ['https://' + pic for pic in pics]
                video_url = ''
                is_exist_video = sel.xpath(
                    './/div[@class="thumbnail"]//video-player').extract_first()
                if is_exist_video:
                    video_url = re.findall(r'src:\'(.*?)\'', is_exist_video)[0]
                    video_url = video_url.replace('&amp;', '&')
                    video_url = 'http:' + video_url
                if not retweet_sel:
                    weibo['pics'] = pics
                    weibo['video_url'] = video_url
                else:
                    weibo['pics'] = ''
                    weibo['video_url'] = ''
                weibo['retweet_id'] = ''
                if retweet_sel and retweet_sel[0].xpath(
                        './/div[@node-type="feed_list_forwardContent"]/a[1]'):
                    retweet = ScrapyWeiboItem()
                    # 安全地提取转发微博ID
                    try:
                        action_data = retweet_sel[0].xpath(
                            './/a[@action-type="feed_list_like"]/@action-data'
                        ).extract_first()
                        if action_data and len(action_data) > 4:
                            retweet['id'] = action_data[4:]
                        else:
                            retweet['id'] = ''
                    except (AttributeError, IndexError) as e:
                        self.logger.warning(f"无法解析转发微博ID: {e}")
                        retweet['id'] = ''
                    # 安全地提取转发微博bid
                    try:
                        retweet_href = retweet_sel[0].xpath(
                            './/p[@class="from"]/a/@href').extract_first()
                        if retweet_href:
                            retweet['bid'] = retweet_href.split('/')[-1].split('?')[0]
                        else:
                            retweet['bid'] = ''
                    except (AttributeError, IndexError) as e:
                        self.logger.warning(f"无法解析转发微博bid: {e}")
                        retweet['bid'] = ''
                    # 安全地提取转发微博用户信息
                    try:
                        info = retweet_sel[0].xpath(
                            './/div[@node-type="feed_list_forwardContent"]/a[1]'
                        )
                        if info:
                            user_href = info[0].xpath('@href').extract_first()
                            if user_href:
                                retweet['user_id'] = user_href.split('/')[-1]
                            else:
                                retweet['user_id'] = ''

                            screen_name = info[0].xpath('@nick-name').extract_first()
                            retweet['screen_name'] = screen_name or ''
                        else:
                            retweet['user_id'] = ''
                            retweet['screen_name'] = ''
                    except (AttributeError, IndexError) as e:
                        self.logger.warning(f"无法解析转发微博用户信息: {e}")
                        retweet['user_id'] = ''
                        retweet['screen_name'] = ''
                    retweet['text'] = retweet_txt_sel.xpath(
                        'string(.)').extract_first().replace('\u200b',
                                                             '').replace(
                        '\ue627', '')
                    retweet['article_url'] = self.get_article_url(
                        retweet_txt_sel)
                    retweet['location'] = self.get_location(retweet_txt_sel)
                    if retweet['location']:
                        retweet['text'] = retweet['text'].replace(
                            '2' + retweet['location'], '')
                    retweet['text'] = retweet['text'][2:].replace(' ', '')
                    if is_long_retweet:
                        retweet['text'] = retweet['text'][:-4]
                    retweet['at_users'] = self.get_at_users(retweet_txt_sel)
                    retweet['topics'] = self.get_topics(retweet_txt_sel)
                    reposts_count = retweet_sel[0].xpath(
                        './/ul[@class="act s-fr"]/li[1]/a[1]/text()'
                    ).extract_first()
                    reposts_count = re.findall(r'\d+.*', reposts_count)
                    retweet['reposts_count'] = reposts_count[
                        0] if reposts_count else '0'
                    comments_count = retweet_sel[0].xpath(
                        './/ul[@class="act s-fr"]/li[2]/a[1]/text()'
                    ).extract_first()
                    comments_count = re.findall(r'\d+.*', comments_count)
                    retweet['comments_count'] = comments_count[
                        0] if comments_count else '0'
                    attitudes_count = retweet_sel[0].xpath(
                        './/a[@class="woo-box-flex woo-box-alignCenter woo-box-justifyCenter"]//span[@class="woo-like-count"]/text()'
                    ).extract_first()
                    attitudes_count = re.findall(r'\d+.*', attitudes_count)
                    retweet['attitudes_count'] = attitudes_count[
                        0] if attitudes_count else '0'
                    created_at = retweet_sel[0].xpath(
                        './/p[@class="from"]/a[1]/text()').extract_first(
                    ).replace(' ', '').replace('\n', '').split('前')[0]
                    retweet['created_at'] = util.standardize_date(created_at)
                    source = retweet_sel[0].xpath(
                        './/p[@class="from"]/a[2]/text()').extract_first()
                    retweet['source'] = source if source else ''
                    retweet['pics'] = pics
                    retweet['video_url'] = video_url
                    retweet['retweet_id'] = ''

                    # 转发微博暂时不设置URL（因为没有爬取转发微博的真实URL逻辑）
                    retweet['weibo_url'] = ''

                    yield {'weibo': retweet, 'keyword': keyword}
                    weibo['retweet_id'] = retweet['id']
                weibo["ip"] = self.get_ip(bid)
                avator = sel.xpath(
                    "div[@class='card']/div[@class='card-feed']/div[@class='avator']"
                )
                if avator:
                    user_auth = avator.xpath('.//svg/@id').extract_first()
                    if user_auth == 'woo_svg_vblue':
                        weibo['user_authentication'] = '蓝V'
                    elif user_auth == 'woo_svg_vyellow':
                        weibo['user_authentication'] = '黄V'
                    elif user_auth == 'woo_svg_vorange':
                        weibo['user_authentication'] = '红V'
                    elif user_auth == 'woo_svg_vgold':
                        weibo['user_authentication'] = '金V'
                    else:
                        weibo['user_authentication'] = '普通用户'
                else:
                    weibo['user_authentication'] = '普通用户'

                # 只使用真实爬取的微博URL，不使用生成的URL（因为生成的URL格式不正确）
                if real_weibo_url:
                    weibo['weibo_url'] = real_weibo_url
                else:
                    # 如果没有爬取到真实URL，则留空
                    weibo['weibo_url'] = ''
                    self.logger.warning(f"未能获取微博真实URL: bid={bid}, user_id={user_id}")

                yield {'weibo': weibo, 'keyword': keyword}
    
    def closed(self, reason):
        """爬虫关闭时的处理

        更新任务状态，记录统计信息

        Args:
            reason: 关闭原因
        """
        # 停止超时监控
        self.stop_timeout_monitor()

        self.status = "completed" if reason == "finished" else "failed"
        self.logger.info(f"爬虫结束，原因: {reason}, 状态: {self.status}")

        # 如果是超时关闭，记录特殊信息
        if reason == "timeout_no_new_data":
            self.logger.info(f"爬虫因数据收集超时而关闭，最终收集微博数量: {self.crawler.stats.get_value('item_scraped_count', 0)}")

        # 获取爬虫统计信息并确保数据可序列化
        stats = dict(self.crawler.stats.get_stats())
        # 处理datetime对象，将其转换为字符串
        for key, value in list(stats.items()):
            if isinstance(value, datetime):
                stats[key] = value.strftime("%Y-%m-%d %H:%M:%S")

        # 保存最终状态
        self.save_status({
            "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": self.status,
            "reason": reason,
            "total_requests": getattr(self, "total_requests", 0),
            "completed_requests": getattr(self, "completed_requests", 0),
            "stats": stats
        })
