"""
数据处理管道模块

定义微博爬虫的各种数据处理器，将爬取的数据保存到不同的存储后端
"""

import os
import json
import copy
import logging
from datetime import datetime
from pathlib import Path
import traceback
import time

import scrapy
from scrapy.exceptions import DropItem
from scrapy.pipelines.files import FilesPipeline
from scrapy.pipelines.images import ImagesPipeline

# 数据库相关导入
import pymongo

from .items import ScrapyWeiboItem


class DuplicatesPipeline:
    """微博去重管道
    
    用于在数据入库前检查是否有重复的微博
    """
    def __init__(self):
        self.ids_seen = set()
        
    def process_item(self, item, spider):
        """处理每个微博条目
        
        Args:
            item: 微博条目
            spider: 爬虫实例
            
        Returns:
            dict: 不重复的微博条目
        
        Raises:
            DropItem: 如果微博ID已存在，抛出此异常
        """
        if item['weibo'].get('id'):
            if item['weibo']['id'] in self.ids_seen:
                raise DropItem(f"重复微博: {item['weibo']['id']}")
            self.ids_seen.add(item['weibo']['id'])
        return item



class JsonPipeline:
    """JSON存储管道
    
    将微博数据保存为JSON文件
    """
    def __init__(self):
        """初始化JSON管道"""
        self.weibo_data = {}
        
    def process_item(self, item, spider):
        """处理每个微博条目
        
        将微博数据按关键词分组保存到内存中
        
        Args:
            item: 微博条目
            spider: 爬虫实例
            
        Returns:
            dict: 处理后的微博条目
        """
        weibo = copy.deepcopy(item['weibo'])
        keyword = item['keyword']
        
        # 初始化关键词对应的数据列表
        if keyword not in self.weibo_data:
            self.weibo_data[keyword] = []
            
        # 添加到关键词对应的列表中
        self.weibo_data[keyword].append(weibo)

        return item
        
    def close_spider(self, spider):
        """爬虫关闭时处理
        
        将内存中的数据保存到JSON文件
        
        Args:
            spider: 爬虫实例
        """
        try:
            # 生成任务ID，格式：当前时间戳
            task_id = spider.task_id if hasattr(spider, 'task_id') else f"task_{int(time.time())}"
            
            # 确保输出目录存在
            base_dir = spider.settings.get('JSON_PATH', spider.settings.get('CSV_PATH', './结果文件'))
            
            # 保存汇总数据
            all_weibo = []
            for keyword, weibo_list in self.weibo_data.items():
                all_weibo.extend(weibo_list)
            
            # 检查是否有数据
            if not all_weibo:
                spider.logger.warning("未收集到任何微博数据")
                spider.save_status({
                    "warning": "未收集到任何微博数据",
                    "possible_reasons": [
                        "搜索关键词可能不够热门",
                        "Cookie可能已失效",
                        "可能被反爬系统拦截",
                        "搜索时间范围内没有相关微博"
                    ]
                })
            
            # 保存为系统格式JSON文件
            file_path = os.path.join(base_dir, f'{task_id}.json')
            with open(file_path, 'w', encoding='utf-8') as f:
                data = {
                    'task_id': task_id,
                    'keywords': list(self.weibo_data.keys()),
                    'count': len(all_weibo),
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'weibo': all_weibo
                }
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            spider.logger.info(f'共收集 {len(all_weibo)} 条微博，保存至 {file_path}')
            
            # 更新爬虫状态
            spider.save_status({
                "status": "completed",
                "total_collected": len(all_weibo),
                "keywords": list(self.weibo_data.keys()),
                "output_file": file_path
            })
            
            # 同时保存每个关键词的单独数据
            for keyword, weibo_list in self.weibo_data.items():
                keyword_dir = os.path.join(base_dir, f'keyword_{keyword.replace("#", "")}')
                if not os.path.isdir(keyword_dir):
                    os.makedirs(keyword_dir)
                
                file_path = os.path.join(keyword_dir, f'{task_id}.json')
                with open(file_path, 'w', encoding='utf-8') as f:
                    data = {
                        'task_id': task_id,
                        'keyword': keyword,
                        'count': len(weibo_list),
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'weibo': weibo_list
                    }
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                spider.logger.info(f'关键词 "{keyword}" 共收集 {len(weibo_list)} 条微博，保存至 {file_path}')
                
        except Exception as e:
            spider.logger.error(f"保存系统数据时发生错误: {e}")
            # 记录详细的错误信息和堆栈跟踪
            spider.logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 更新爬虫状态
            spider.save_status({
                "status": "error",
                "error": f"保存系统数据时发生错误: {str(e)}"
            })


class SystemPipeline:
    """系统集成存储管道
    
    将微博数据保存为系统后续处理所需的格式
    """
    def __init__(self):
        """初始化系统集成管道"""
        self.weibo_data = {}
        self.error_count = 0
        self.max_errors = 10  # 最大允许错误数
        
    def process_item(self, item, spider):
        """处理每个微博条目
        
        将微博数据按关键词分组保存到内存中，并转换为系统所需格式
        
        Args:
            item: 微博条目
            spider: 爬虫实例
            
        Returns:
            dict: 处理后的微博条目
        """
        try:
            weibo = copy.deepcopy(item['weibo'])
            keyword = item['keyword']
            
            # 初始化关键词对应的数据列表
            if keyword not in self.weibo_data:
                self.weibo_data[keyword] = []
                
            # 转换为系统格式
            system_weibo = {
                'id': weibo['id'],
                'bid': weibo['bid'],
                'content': weibo['text'],
                'user': {
                    'id': weibo['user_id'],
                    'name': weibo['screen_name'],
                    'authentication': weibo['user_authentication']
                },
                'location': weibo['location'],
                'topics': weibo['topics'],
                'at_users': weibo['at_users'],
                'stats': {
                    'reposts': weibo['reposts_count'],
                    'comments': weibo['comments_count'],
                    'likes': weibo['attitudes_count']
                },
                'created_at': weibo['created_at'],
                'source': weibo['source'],
                'article_url': weibo['article_url'],
                'media': {
                    'pics': weibo['pics'],
                    'video_url': weibo['video_url']
                },
                'ip': weibo['ip'],
                'retweet_id': weibo['retweet_id']
            }
            
            # 添加到关键词对应的列表中
            self.weibo_data[keyword].append(system_weibo)
            
            # 记录进度 - 使用简单的计数显示，避免在pipeline中使用复杂的进度条
            total_count = sum(len(weibos) for weibos in self.weibo_data.values())
            if total_count % 50 == 0:  # 每收集50条记录一次，减少日志频率
                spider.logger.info(f"[数据收集] 已收集 {total_count} 条微博")

            return item
            
        except Exception as e:
            self.error_count += 1
            spider.logger.error(f"处理微博数据时发生错误: {e}")
            
            # 如果错误次数过多，记录到爬虫状态
            if self.error_count >= self.max_errors:
                spider.logger.error(f"处理数据错误次数过多 ({self.error_count})，可能存在系统问题")
                spider.save_status({"error": f"处理数据错误次数过多: {self.error_count}"})
            
            # 继续处理其他条目
            return item
        
    def close_spider(self, spider):
        """爬虫关闭时处理
        
        将内存中的数据保存到系统格式的JSON文件
        
        Args:
            spider: 爬虫实例
        """
        try:
            # 生成任务ID，格式：当前时间
            task_id = spider.task_id if hasattr(spider, 'task_id') else f"task_{int(time.time())}"
            
            # 确保输出目录存在
            base_dir = spider.settings.get('SYSTEM_DATA_DIR', './data/raw/weibo')
            if not os.path.isdir(base_dir):
                os.makedirs(base_dir)
            
            # 保存汇总数据
            all_weibo = []
            for keyword, weibo_list in self.weibo_data.items():
                all_weibo.extend(weibo_list)
            
            # 检查是否有数据
            if not all_weibo:
                spider.logger.warning("未收集到任何微博数据")
                spider.save_status({
                    "warning": "未收集到任何微博数据",
                    "possible_reasons": [
                        "搜索关键词可能不够热门",
                        "Cookie可能已失效",
                        "可能被反爬系统拦截",
                        "搜索时间范围内没有相关微博"
                    ]
                })
            
            # 保存为系统格式JSON文件
            file_path = os.path.join(base_dir, f'{task_id}.json')
            with open(file_path, 'w', encoding='utf-8') as f:
                data = {
                    'task_id': task_id,
                    'keywords': list(self.weibo_data.keys()),
                    'count': len(all_weibo),
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'weibo': all_weibo
                }
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            spider.logger.info(f'共收集{len(all_weibo)} 条微博，保存到{file_path}')
            
            # 更新爬虫状态
            spider.save_status({
                "status": "completed",
                "total_collected": len(all_weibo),
                "keywords": list(self.weibo_data.keys()),
                "output_file": file_path
            })
            
            # 同时保存每个关键词的单独数据
            for keyword, weibo_list in self.weibo_data.items():
                keyword_dir = os.path.join(base_dir, f'keyword_{keyword.replace("#", "")}')
                if not os.path.isdir(keyword_dir):
                    os.makedirs(keyword_dir)
                
                file_path = os.path.join(keyword_dir, f'{task_id}.json')
                with open(file_path, 'w', encoding='utf-8') as f:
                    data = {
                        'task_id': task_id,
                        'keyword': keyword,
                        'count': len(weibo_list),
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'weibo': weibo_list
                    }
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                spider.logger.info(f'关键词"{keyword}" 共收集{len(weibo_list)} 条微博，保存到{file_path}')
                
        except Exception as e:
            spider.logger.error(f"保存系统数据时发生错误: {e}")
            # 记录详细的错误信息和堆栈跟踪
            spider.logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 更新爬虫状态
            spider.save_status({
                "status": "error",
                "error": f"保存系统数据时发生错误: {str(e)}"
            })


class MongoPipeline:
    """MongoDB存储管道
    
    将微博数据存储到MongoDB数据库
    """
    def open_spider(self, spider):
        """爬虫开启时调用，连接MongoDB数据库

        Args:
            spider: 爬虫实例

        Raises:
            pymongo.errors.ConnectionFailure: 如果连接MongoDB失败
        """
        if pymongo is None:
            spider.pymongo_error = True
            spider.mongo_error = True
            spider.logger.error("pymongo模块未安装，无法使用MongoDB存储")
            return

        try:
            # 获取MongoDB配置
            db_uri = spider.settings.get('MONGO_URI', 'localhost:27017')
            db_name = spider.settings.get('MONGO_DB', 'weibo')

            # 连接MongoDB
            self.client = pymongo.MongoClient(db_uri)
            self.db = self.client[db_name]

            # 创建索引，用于去重和性能优化
            self.db['weibo'].create_index([('id', pymongo.ASCENDING)], unique=True)
            self.db['keyword'].create_index([('keyword', pymongo.ASCENDING)], unique=True)
        except Exception as e:
            spider.pymongo_error = True
            spider.mongo_error = True
            spider.logger.error(f"连接MongoDB失败: {e}")
    
    def process_item(self, item, spider):
        """处理每个微博条目

        将微博数据存储到MongoDB

        Args:
            item: 微博条目
            spider: 爬虫实例

        Returns:
            dict: 处理后的微博条目
        """
        # 检查MongoDB连接状态
        if hasattr(spider, 'mongo_error') and spider.mongo_error:
            return item

        try:
            # 保存微博数据
            weibo_data = dict(item['weibo'])
            keyword = item['keyword']
            
            # 更新微博集合，使用upsert防止重复插入
            self.db['weibo'].update_one({'id': weibo_data['id']}, 
                                      {'$set': weibo_data}, 
                                      upsert=True)
            
            # 更新关键词集合
            self.db['keyword'].update_one(
                {'keyword': keyword},
                {'$set': {'keyword': keyword, 'last_update': datetime.now()}},
                upsert=True
            )
            
            # 建立微博和关键词的关联关系
            self.db['weibo_keyword'].update_one(
                {'weibo_id': weibo_data['id'], 'keyword': keyword},
                {'$set': {
                    'weibo_id': weibo_data['id'], 
                    'keyword': keyword,
                    'created_at': weibo_data['created_at']
                }},
                upsert=True
            )
        except Exception as e:
            spider.logger.error(f"保存微博到MongoDB失败: {e}")
            
        return item
        
    def close_spider(self, spider):
        """爬虫关闭时调用，关闭MongoDB连接

        Args:
            spider: 爬虫实例
        """
        try:
            if hasattr(self, 'client'):
                self.client.close()
        except Exception as e:
            spider.logger.error(f"关闭MongoDB连接失败: {e}")




class MyImagesPipeline(ImagesPipeline):
    """图片下载管道
    
    下载微博中的图片
    """
    def get_media_requests(self, item, info):
        """获取媒体请求

        从微博条目中提取图片URL并生成下载请求

        Args:
            item: 微博条目
            info: 爬虫信息

        Returns:
            list: 请求列表
        """
        if isinstance(item, dict) and 'weibo' in item:
            weibo = item['weibo']
            if weibo.get('pics'):
                pic_urls = weibo['pics']
                if isinstance(pic_urls, list):
                    for pic_url in pic_urls:
                        if pic_url:
                            # 添加必要的请求头以绕过防盗链
                            headers = {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                                'Referer': 'https://weibo.com/',
                                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                                'Accept-Encoding': 'gzip, deflate, br',
                                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                'Sec-Ch-Ua-Mobile': '?0',
                                'Sec-Ch-Ua-Platform': '"Windows"',
                                'Sec-Fetch-Dest': 'image',
                                'Sec-Fetch-Mode': 'no-cors',
                                'Sec-Fetch-Site': 'cross-site',
                            }
                            yield scrapy.Request(pic_url, headers=headers, meta={'item': item})
                else:
                    self.logger.warning(f"pics字段不是列表: {pic_urls}")
    
    def file_path(self, request, response=None, info=None):
        """指定文件保存路径
        
        Args:
            request: 请求对象
            response: 响应对象
            info: 爬虫信息
            
        Returns:
            str: 文件保存路径
        """
        if request.meta.get('item'):
            item = request.meta['item']
            weibo = item['weibo']
            keyword = item['keyword']
            
            # 格式化日期
            date_str = datetime.strptime(weibo['created_at'], '%Y-%m-%d %H:%M').strftime('%Y%m%d_%H%M%S')
            
            # 获取文件扩展名
            url = request.url
            file_name = url.split('/')[-1]
            file_suffix = '.jpg'  # 默认后缀
            if '.' in file_name:
                file_suffix = f".{file_name.split('.')[-1]}"
                if len(file_suffix) > 5:
                    file_suffix = '.jpg'
                    
            # 文件保存路径: 关键词/ID_日期_随机数.后缀
            filename = f"{keyword}/{weibo['id']}_{date_str}_{hash(url) % 1000}{file_suffix}"
            return filename
        else:
            url = request.url
            file_name = url.split('/')[-1]
            return f"unknown/{file_name}"


class MyVideoPipeline(FilesPipeline):
    """视频下载管道
    
    下载微博中的视频
    """
    def get_media_requests(self, item, info):
        """获取媒体请求

        从微博条目中提取视频URL并生成下载请求

        Args:
            item: 微博条目
            info: 爬虫信息

        Returns:
            list: 请求列表
        """
        if isinstance(item, dict) and 'weibo' in item:
            weibo = item['weibo']
            if weibo.get('video_url'):
                # 添加必要的请求头以绕过防盗链
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer': 'https://weibo.com/',
                    'Accept': 'video/webm,video/ogg,video/*;q=0.9,application/ogg;q=0.7,audio/*;q=0.6,*/*;q=0.5',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'video',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'cross-site',
                }
                yield scrapy.Request(weibo['video_url'], headers=headers, meta={'item': item})
    
    def file_path(self, request, response=None, info=None):
        """指定文件保存路径
        
        Args:
            request: 请求对象
            response: 响应对象
            info: 爬虫信息
            
        Returns:
            str: 文件保存路径
        """
        if request.meta.get('item'):
            item = request.meta['item']
            weibo = item['weibo']
            keyword = item['keyword']
            
            # 格式化日期
            date_str = datetime.strptime(weibo['created_at'], '%Y-%m-%d %H:%M').strftime('%Y%m%d_%H%M%S')
            
            # 获取文件扩展名
            url = request.url
            file_name = url.split('/')[-1]
            file_suffix = '.mp4'  # 默认后缀
            if '.' in file_name:
                file_suffix = f".{file_name.split('.')[-1]}"
                if len(file_suffix) > 5:
                    file_suffix = '.mp4'
                    
            # 文件保存路径: 关键词/ID_日期.后缀
            filename = f"{keyword}/{weibo['id']}_{date_str}{file_suffix}"
            return filename
        else:
            url = request.url
            file_name = url.split('/')[-1]
            return f"unknown/{file_name}"


# 添加JsonWriterPipeline
class JsonWriterPipeline(object):
    """将爬取的微博数据写入JSON文件的管道"""
    
    def __init__(self, json_path=None):
        self.json_path = json_path
        self.items = []
        
    @classmethod
    def from_crawler(cls, crawler):
        return cls(
            json_path=crawler.settings.get('JSON_OUTPUT_PATH')
        )
        
    def open_spider(self, spider):
        # 确保输出目录存在
        if self.json_path:
            path = Path(self.json_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 记录任务开始时间
            self.start_time = datetime.now()
    
    def process_item(self, item, spider):
        weibo_data = dict(item['weibo'])
        
        # 转换为标准格式
        post = {
            'id': weibo_data.get('id', ''),
            'bid': weibo_data.get('bid', ''),
            'user_id': weibo_data.get('user_id', ''),
            'screen_name': weibo_data.get('screen_name', ''),
            'text': weibo_data.get('text', ''),
            'article_url': weibo_data.get('article_url', ''),
            'location': weibo_data.get('location', ''),
            'created_at': weibo_data.get('created_at', ''),
            'source': weibo_data.get('source', ''),
            'attitudes_count': weibo_data.get('attitudes_count', 0),
            'comments_count': weibo_data.get('comments_count', 0),
            'reposts_count': weibo_data.get('reposts_count', 0),
            'topics': weibo_data.get('topics', []),
            'at_users': weibo_data.get('at_users', []),
            'pics': weibo_data.get('pics', []),
            'video_url': weibo_data.get('video_url', ''),
            'retweet_id': weibo_data.get('retweet_id', ''),
            'weibo_url': weibo_data.get('weibo_url', ''),
            'user': {
                'id': weibo_data.get('user_id', ''),
                'screen_name': weibo_data.get('screen_name', ''),
                'verified_type': self._get_verified_type(weibo_data.get('user_authentication', '')),
                'verified_reason': weibo_data.get('user_authentication', '')
            }
        }
        
        self.items.append(post)
        return item
    
    def _get_verified_type(self, auth_str):
        """根据认证信息获取认证类型"""
        if not auth_str or auth_str == '普通用户':
            return -1
        elif auth_str == '黄V':
            return 0  # 个人认证
        elif auth_str == '蓝V':
            return 1  # 企业认证
        elif auth_str == '红V':
            return 2  # 媒体认证
        elif auth_str == '金V':
            return 3  # 政府认证
        else:
            return 0  # 默认个人认证
    
    def close_spider(self, spider):
        if not self.json_path or not self.items:
            return
            
        # 获取任务ID，如果没有则使用时间戳
        task_id = getattr(spider, 'task_id', datetime.now().strftime('%Y%m%d%H%M%S'))
        
        # 写入JSON文件
        output_data = {
            'task_id': task_id,
            'keyword': getattr(spider, 'keyword_list', []),
            'search_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_count': len(self.items),
            'posts': self.items
        }
        
        with open(self.json_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
            
        spider.logger.info(f"已将{len(self.items)}条微博数据保存至{self.json_path}")
