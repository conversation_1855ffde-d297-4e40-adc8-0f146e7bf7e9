"""
模型模块
定义系统中所有数据模型和数据结构
"""

# 导入常量和枚举
from .constants import (
    RiskLevel, TaskStatus, DataSource,
    get_risk_level, get_risk_level_text, 
    compare_risk_levels, get_highest_risk_level,
    get_task_status, get_task_status_text
)

# 导入统一数据格式模型
from .data_formats import (
    WeiboItem, MediaItem, HazardItem, RiskAssessment, TaskResult
)

# 导入报告模型
from .report import (
    RiskItem, Report
)

# 明确导出项
__all__ = [
    # 常量和枚举
    'RiskLevel', 'TaskStatus', 'DataSource',
    'get_risk_level', 'get_risk_level_text',
    'compare_risk_levels', 'get_highest_risk_level',
    'get_task_status', 'get_task_status_text',
    
    # 统一数据格式模型
    'WeiboItem', 'MediaItem', 'HazardItem', 'RiskAssessment', 'TaskResult',
    
    # 报告模型
    'RiskItem', 'Report'
] 
