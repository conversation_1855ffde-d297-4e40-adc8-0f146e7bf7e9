"""
常量和枚举定义
定义系统中使用的所有常量和枚举类型
"""

from enum import Enum, auto
from typing import Dict, Union, Optional, Any, List

class RiskLevel(str, Enum):
    """
    风险等级枚举
    
    系统中所有风险等级必须使用此枚举类型，禁止使用字符串字面量或其他表示方式
    
    属性:
        CRITICAL: 极高风险，必须立即处理
        MEDIUM: 中风险，建议整改优化
        LOW: 低风险，提醒关注观察
        INCOMPLETE: 信息不完整
    
    示例:
        >>> risk = RiskLevel.CRITICAL
        >>> str(risk)
        '必须立即处理'
        >>> RiskLevel.from_string('必须立即处理') == RiskLevel.CRITICAL
        True
    """
    CRITICAL = "必须立即处理"
    MEDIUM = "建议整改优化"
    LOW = "提醒关注观察"
    INCOMPLETE = "信息不完整"
    
    @classmethod
    def from_string(cls, value: str) -> 'RiskLevel':
        """
        从字符串获取枚举值
        
        Args:
            value: 风险等级的字符串表示，必须与枚举值完全匹配
                
        Returns:
            RiskLevel: 对应的风险等级枚举值
                
        Raises:
            ValueError: 当提供的字符串不匹配任何风险等级时抛出
        
        示例:
            >>> RiskLevel.from_string('建议整改优化')
            <RiskLevel.MEDIUM: '建议整改优化'>
        """
        for level in cls:
            if level.value == value:
                return level
        raise ValueError(f"未知的风险等级: {value}")
    
    def __str__(self) -> str:
        """
        返回风险等级的字符串表示
        
        Returns:
            str: 风险等级的字符串表示
        
        示例:
            >>> str(RiskLevel.MEDIUM)
            '提醒关注观察'
        """
        return self.value

# 风险级别对应的数值，用于排序和比较
RISK_LEVEL_VALUE = {
    RiskLevel.CRITICAL: 4,
    RiskLevel.MEDIUM: 3,
    RiskLevel.LOW: 2,
    RiskLevel.INCOMPLETE: 1
}

def get_risk_level(value: Union[str, RiskLevel, None]) -> RiskLevel:
    """
    获取标准化的风险等级
    
    此函数是系统中唯一应该用于风险等级转换的方法，确保所有风险等级表示的一致性
    
    Args:
        value: 风险等级值，可以是以下类型:
            - RiskLevel枚举值: 直接返回
            - 字符串: 尝试匹配RiskLevel的值，如"必须立即处理"将返回RiskLevel.CRITICAL
            - None: 返回默认值RiskLevel.INCOMPLETE
            
    Returns:
        RiskLevel: 标准化的风险等级枚举值
    
    示例:
        >>> get_risk_level("必须立即处理")
        <RiskLevel.CRITICAL: '必须立即处理'>
        >>> get_risk_level(RiskLevel.MEDIUM)
        <RiskLevel.MEDIUM: '建议整改优化'>
        >>> get_risk_level(None)
        <RiskLevel.INCOMPLETE: '信息不完整'>
        >>> get_risk_level("未知风险")  # 无法识别的风险等级默认为INCOMPLETE
        <RiskLevel.INCOMPLETE: '信息不完整'>
    """
    if value is None:
        return RiskLevel.INCOMPLETE
    
    if isinstance(value, RiskLevel):
        return value
        
    try:
        return RiskLevel.from_string(value)
    except ValueError:
        # 对于无法识别的风险等级，返回默认值
        return RiskLevel.INCOMPLETE

def get_risk_level_text(risk_level: Union[str, RiskLevel, None]) -> str:
    """
    获取风险等级的显示文本
    
    Args:
        risk_level: 风险等级，可以是字符串、RiskLevel枚举或None
            
    Returns:
        str: 风险等级的显示文本
    
    示例:
        >>> get_risk_level_text(RiskLevel.CRITICAL)
        '必须立即处理'
        >>> get_risk_level_text("建议整改优化")
        '建议整改优化'
        >>> get_risk_level_text(None)
        '信息不完整'
    """
    level = get_risk_level(risk_level)
    return level.value

def compare_risk_levels(level1: Union[str, RiskLevel, None], level2: Union[str, RiskLevel, None]) -> int:
    """
    比较两个风险等级的高低
    
    Args:
        level1: 第一个风险等级
        level2: 第二个风险等级
            
    Returns:
        int: 比较结果
            - 正数: level1 > level2
            - 0: level1 == level2
            - 负数: level1 < level2
    
    示例:
        >>> compare_risk_levels(RiskLevel.CRITICAL, RiskLevel.MEDIUM)
        1
        >>> compare_risk_levels("提醒关注观察", "必须立即处理")
        -2
    """
    risk1 = get_risk_level(level1)
    risk2 = get_risk_level(level2)
    
    return RISK_LEVEL_VALUE.get(risk1, 0) - RISK_LEVEL_VALUE.get(risk2, 0)

def get_highest_risk_level(levels: List[Union[str, RiskLevel, None]]) -> RiskLevel:
    """
    获取多个风险等级中的最高等级
    
    Args:
        levels: 风险等级列表
            
    Returns:
        RiskLevel: 最高的风险等级
    
    示例:
        >>> get_highest_risk_level([RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.CRITICAL])
        <RiskLevel.CRITICAL: '必须立即处理'>
        >>> get_highest_risk_level(["信息不完整", "必须立即处理"])
        <RiskLevel.CRITICAL: '必须立即处理'>
    """
    if not levels:
        return RiskLevel.INCOMPLETE
        
    standardized_levels = [get_risk_level(level) for level in levels]
    return max(standardized_levels, key=lambda x: RISK_LEVEL_VALUE.get(x, 0))

class TaskStatus(str, Enum):
    """
    任务状态枚举
    
    系统中所有任务状态必须使用此枚举类型，禁止使用字符串字面量
    
    属性:
        CREATED: 任务已创建但尚未开始
        PENDING: 任务等待执行
        RUNNING: 任务正在执行
        COMPLETED: 任务已成功完成
        FAILED: 任务执行失败
        CANCELED: 任务被取消
    """
    CREATED = "created"
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"
    
    @classmethod
    def from_string(cls, value: str) -> 'TaskStatus':
        """
        从字符串获取枚举值
        
        Args:
            value: 任务状态的字符串表示，必须与枚举值完全匹配
                
        Returns:
            TaskStatus: 对应的任务状态枚举值
                
        Raises:
            ValueError: 当提供的字符串不匹配任何任务状态时抛出
        """
        for status in cls:
            if status.value == value:
                return status
        raise ValueError(f"未知的任务状态: {value}")
    
    def __str__(self) -> str:
        """
        返回任务状态的字符串表示
        
        Returns:
            str: 任务状态的字符串表示
        """
        return self.value

# 任务状态对应的显示文本
TASK_STATUS_TEXT = {
    TaskStatus.CREATED: "已创建",
    TaskStatus.PENDING: "等待中",
    TaskStatus.RUNNING: "执行中",
    TaskStatus.COMPLETED: "已完成",
    TaskStatus.FAILED: "失败",
    TaskStatus.CANCELED: "已取消"
}

def get_task_status(value: Union[str, TaskStatus, None]) -> TaskStatus:
    """
    获取标准化的任务状态
    
    Args:
        value: 任务状态值，可以是字符串、TaskStatus枚举或None
            
    Returns:
        TaskStatus: 标准化的任务状态枚举值
        
    示例:
        >>> get_task_status("running")
        <TaskStatus.RUNNING: 'running'>
        >>> get_task_status(TaskStatus.COMPLETED)
        <TaskStatus.COMPLETED: 'completed'>
        >>> get_task_status(None)
        <TaskStatus.CREATED: 'created'>
    """
    if value is None:
        return TaskStatus.CREATED
    
    if isinstance(value, TaskStatus):
        return value
        
    try:
        return TaskStatus.from_string(value)
    except ValueError:
        return TaskStatus.CREATED

def get_task_status_text(status: Union[str, TaskStatus, None]) -> str:
    """
    获取任务状态的显示文本
    
    Args:
        status: 任务状态，可以是字符串、TaskStatus枚举或None
            
    Returns:
        str: 任务状态的显示文本
    """
    task_status = get_task_status(status)
    return TASK_STATUS_TEXT.get(task_status, "未知")

class DataSource(str, Enum):
    """数据源枚举"""
    WEIBO = "weibo"      # 微博数据源
    NEWS = "news"        # 新闻数据源
    REPORT = "report"    # 报告数据源
    MANUAL = "manual"    # 手动输入数据源
    
    def __str__(self) -> str:
        """返回数据源的字符串表示"""
        return self.value 



