"""
Neo4j数据库连接模块，提供与Neo4j图数据库的连接和操作功能
支持Neo4j 6.0向量索引和语义查询功能
"""

import time
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from functools import wraps
import threading
from queue import Queue, Empty
import contextlib
import sys

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent.parent.parent.parent  # 指向sys3.0目录
sys.path.insert(0, str(ROOT_DIR))

# 使用官方neo4j驱动，支持向量索引
from neo4j import GraphDatabase, Driver, Session, Result
from neo4j.exceptions import ServiceUnavailable, AuthError, ClientError

# 导入项目工具
from utils.logger import get_logger, LoggerMixin
from utils.exceptions import NetworkException

# 创建装饰器使用的日志记录器
db_logger = get_logger("database_operations")

def retry_on_error(max_retries: int = 3, delay: float = 1.0):
    """数据库操作重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_error = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except (ServiceUnavailable, ClientError, ConnectionError) as e:
                    last_error = e
                    if attempt < max_retries - 1:
                        db_logger.warning(f"操作失败，{delay}秒后重试 ({attempt + 1}/{max_retries}): {str(e)}")
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                    continue
            raise NetworkException(f"操作失败，已重试{max_retries}次: {str(last_error)}")
        return wrapper
    return decorator

class Neo4jConnectionPool:
    """Neo4j数据库连接池 - 使用官方驱动支持向量索引"""

    def __init__(self, uri: str, user: str, password: str, max_connections: int = 5):
        self.uri = uri
        self.user = user
        self.password = password
        self.max_connections = max_connections

        # 使用官方驱动创建连接池
        try:
            # 尝试新版本的认证方式
            from neo4j import basic_auth
            self.driver = GraphDatabase.driver(
                uri,
                auth=basic_auth(user, password),
                max_connection_pool_size=max_connections
            )
        except Exception as e:
            # 如果失败，尝试直接使用元组认证
            self.driver = GraphDatabase.driver(
                uri,
                auth=(user, password),
                max_connection_pool_size=max_connections
            )

        # 测试连接
        self._test_connection()

    def _test_connection(self) -> None:
        """测试数据库连接"""
        try:
            with self.driver.session() as session:
                session.run("RETURN 1")
        except AuthError as e:
            raise NetworkException(f"Neo4j认证失败: {str(e)}")
        except ServiceUnavailable as e:
            raise NetworkException(f"Neo4j服务不可用: {str(e)}")
        except Exception as e:
            raise NetworkException(f"Neo4j连接测试失败: {str(e)}")

    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.driver.session()

    def close_all(self):
        """关闭所有连接"""
        if self.driver:
            self.driver.close()

    def close(self):
        """关闭连接池"""
        self.close_all()

class Neo4jConnection(LoggerMixin):
    """Neo4j数据库连接管理类 - 支持向量索引"""

    def __init__(self, uri: str, user: str, password: str, database: str = "neo4j"):
        """
        初始化Neo4j连接

        Args:
            uri: Neo4j数据库URI
            user: 用户名
            password: 密码
            database: 数据库名称
        """
        super().__init__()
        self.uri = uri
        self.user = user
        self.password = password
        self.database = database
        self.pool = Neo4jConnectionPool(uri, user, password, 5)
        self.is_connected = False

    @contextlib.contextmanager
    def get_session(self):
        """获取数据库会话的上下文管理器"""
        session = None
        try:
            session = self.pool.get_session()
            yield session
        finally:
            if session:
                session.close()
    
    def connect(self) -> bool:
        """建立初始连接并验证配置"""
        try:
            with self.get_session() as graph:
                graph.run("MATCH (n) RETURN count(n) LIMIT 1")
            self.is_connected = True
            self.log_info("成功连接到Neo4j数据库")
            return True
        except Exception as e:
            self.is_connected = False
            self.log_error(f"Neo4j连接失败: {str(e)}")
            return False
    
    def close(self) -> None:
        """关闭所有数据库连接"""
        self.pool.close_all()
        self.is_connected = False
        self.log_info("所有数据库连接已关闭")
    
    @retry_on_error()
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行Cypher查询"""
        if not self.is_connected:
            raise NetworkException("数据库未连接")

        with self.get_session() as session:
            try:
                result = session.run(query, params or {})
                return [dict(record) for record in result]
            except ClientError as e:
                raise NetworkException(f"查询语法错误: {str(e)}")
            except ServiceUnavailable as e:
                raise NetworkException(f"数据库服务不可用: {str(e)}")
            except Exception as e:
                raise NetworkException(f"查询失败: {str(e)}")

    def execute_vector_query(self, index_name: str, query_vector: List[float],
                           top_k: int = 10, min_score: float = 0.0) -> List[Dict[str, Any]]:
        """
        执行向量相似度查询

        Args:
            index_name: 向量索引名称
            query_vector: 查询向量
            top_k: 返回结果数量
            min_score: 最小相似度分数

        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        if not self.is_connected:
            raise NetworkException("数据库未连接")

        query = f"""
        CALL db.index.vector.queryNodes($index_name, $top_k, $query_vector)
        YIELD node, score
        WHERE score >= $min_score
        RETURN node, score
        ORDER BY score DESC
        """

        params = {
            "index_name": index_name,
            "top_k": top_k,
            "query_vector": query_vector,
            "min_score": min_score
        }

        return self.execute_query(query, params)
    
    @retry_on_error()
    def create_node(self, label: str, properties: Dict[str, Any]) -> Dict[str, Any]:
        """创建节点"""
        if not self.is_connected:
            raise NetworkException("数据库未连接")

        with self.get_session() as session:
            try:
                query = f"""
                CREATE (n:{label})
                SET n = $properties
                RETURN n
                """
                result = session.run(query, properties=properties)
                record = result.single()
                return dict(record['n']) if record else {}
            except Exception as e:
                raise NetworkException(f"创建节点失败 ({label}): {str(e)}")

    @retry_on_error()
    def create_relationship(self, start_node_id: int, rel_type: str, end_node_id: int,
                          properties: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """创建关系"""
        if not self.is_connected:
            raise NetworkException("数据库未连接")

        with self.get_session() as session:
            try:
                query = f"""
                MATCH (a), (b)
                WHERE elementId(a) = $start_id AND elementId(b) = $end_id
                CREATE (a)-[r:{rel_type}]->(b)
                SET r = $properties
                RETURN r
                """
                result = session.run(query, {
                    'start_id': start_node_id,
                    'end_id': end_node_id,
                    'properties': properties or {}
                })
                record = result.single()
                return dict(record['r']) if record else {}
            except Exception as e:
                raise NetworkException(f"创建关系失败: {str(e)}")

    @retry_on_error()
    def find_node(self, label: str, property_key: str, property_value: Any) -> Optional[Dict[str, Any]]:
        """查找节点"""
        if not self.is_connected:
            raise NetworkException("数据库未连接")

        with self.get_session() as session:
            try:
                query = f"""
                MATCH (n:{label})
                WHERE n.{property_key} = $value
                RETURN n
                LIMIT 1
                """
                result = session.run(query, value=property_value)
                record = result.single()
                return dict(record['n']) if record else None
            except Exception as e:
                raise NetworkException(f"查找节点失败: {str(e)}")

    @retry_on_error()
    def find_relationships(self, start_node_id: Optional[int] = None,
                         rel_type: Optional[str] = None,
                         end_node_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """查找关系"""
        if not self.is_connected:
            raise NetworkException("数据库未连接")

        with self.get_session() as session:
            try:
                conditions = []
                params = {}

                if start_node_id is not None:
                    conditions.append("elementId(a) = $start_id")
                    params['start_id'] = start_node_id

                if end_node_id is not None:
                    conditions.append("elementId(b) = $end_id")
                    params['end_id'] = end_node_id

                rel_pattern = f"[r{':' + rel_type if rel_type else ''}]"
                where_clause = " AND ".join(conditions) if conditions else "true"

                query = f"""
                MATCH (a)-{rel_pattern}->(b)
                WHERE {where_clause}
                RETURN r
                """

                result = session.run(query, params)
                return [dict(record['r']) for record in result]
            except Exception as e:
                raise NetworkException(f"查找关系失败: {str(e)}")

    def close(self) -> None:
        """关闭数据库连接"""
        self.pool.close_all()
        self.is_connected = False
        self.log_info("所有数据库连接已关闭")

    def create_vector_index(self, index_name: str, label: str, property_name: str,
                          dimensions: int = 384, similarity_function: str = 'cosine') -> bool:
        """
        创建向量索引

        Args:
            index_name: 索引名称
            label: 节点标签
            property_name: 属性名称
            dimensions: 向量维度
            similarity_function: 相似度函数 ('cosine', 'euclidean')

        Returns:
            bool: 创建是否成功
        """
        try:
            query = f"""
            CREATE VECTOR INDEX {index_name} IF NOT EXISTS
            FOR (n:{label})
            ON (n.{property_name})
            OPTIONS {{
                indexConfig: {{
                    `vector.dimensions`: {dimensions},
                    `vector.similarity_function`: '{similarity_function}'
                }}
            }}
            """

            self.execute_query(query)
            self.log_info(f"向量索引 {index_name} 创建成功")
            return True

        except Exception as e:
            self.log_error(f"创建向量索引失败: {str(e)}", e)
            return False

    def create_all_vector_indices(self) -> bool:
        """
        创建所有必要的向量索引（5个节点类型）

        Returns:
            bool: 是否全部创建成功
        """
        try:
            indices_config = [
                {
                    'name': 'case_embeddings',
                    'label': 'Case',
                    'property': 'embedding',
                    'dimensions': 384
                },
                {
                    'name': 'hazard_behavior_embeddings',
                    'label': 'HazardBehavior',
                    'property': 'embedding',
                    'dimensions': 384
                },
                {
                    'name': 'warning_signal_embeddings',
                    'label': 'WarningSignal',
                    'property': 'embedding',
                    'dimensions': 384
                },
                {
                    'name': 'trigger_cause_embeddings',
                    'label': 'TriggerCause',
                    'property': 'embedding',
                    'dimensions': 384
                },
                {
                    'name': 'damage_embeddings',
                    'label': 'Damage',
                    'property': 'embedding',
                    'dimensions': 384
                }
            ]

            success_count = 0
            for config in indices_config:
                if self.create_vector_index(
                    index_name=config['name'],
                    label=config['label'],
                    property_name=config['property'],
                    dimensions=config['dimensions']
                ):
                    success_count += 1
                    self.log_info(f"向量索引 {config['name']} 创建成功")
                else:
                    self.log_warning(f"向量索引 {config['name']} 创建失败")

            total_indices = len(indices_config)
            self.log_info(f"向量索引创建完成: {success_count}/{total_indices}")
            return success_count == total_indices

        except Exception as e:
            self.log_error(f"创建向量索引失败: {str(e)}", e)
            return False

    def vectorize_existing_nodes(self) -> bool:
        """
        为现有节点生成向量（5个节点类型）

        Returns:
            bool: 是否成功
        """
        try:
            from .embedding_service import get_embedding_service
            embedding_service = get_embedding_service()

            # 向量化所有5种节点类型
            node_types = ['Case', 'HazardBehavior', 'WarningSignal', 'TriggerCause', 'Damage']

            for node_type in node_types:
                self._vectorize_nodes_by_label(node_type, embedding_service)

            print("✅ 所有节点向量化完成")
            return True

        except Exception as e:
            self.log_error(f"节点向量化失败: {str(e)}", e)
            return False

    def _vectorize_nodes_by_label(self, label: str, embedding_service) -> None:
        """
        为指定标签的节点生成向量

        Args:
            label: 节点标签
            embedding_service: 向量化服务
        """
        try:
            # 获取所有指定标签的节点（使用实际的字段结构）
            query = f"""
            MATCH (n:{label})
            WHERE n.embedding IS NULL
            RETURN elementId(n) as node_id, n.label as label, n.type as type, properties(n) as props
            """
            results = self.execute_query(query)

            if not results:
                return  # 静默跳过已有向量的节点

            # 只在开始时显示一次简洁信息
            print(f"🔧 正在向量化 {label} 节点 ({len(results)} 个)...")

            # 批量处理节点
            batch_size = 32
            for i in range(0, len(results), batch_size):
                batch = results[i:i + batch_size]

                # 准备文本数据
                texts = []
                node_ids = []

                for result in batch:
                    node_id = result['node_id']
                    node_label = result.get('label', '')
                    node_type = result.get('type', '')
                    props = result.get('props', {})

                    # 组合文本用于向量化
                    text_parts = []
                    if node_label:
                        text_parts.append(node_label)
                    if node_type:
                        text_parts.append(node_type)

                    # 添加其他有用的属性
                    for key, value in props.items():
                        if key not in ['id', 'label', 'type', 'embedding'] and value:
                            text_parts.append(str(value))

                    combined_text = ' '.join(text_parts).strip()
                    if not combined_text:
                        combined_text = f"{label} node"  # 默认文本

                    texts.append(combined_text)
                    node_ids.append(node_id)

                # 批量生成向量
                embeddings = embedding_service.encode_batch(texts)

                # 更新节点向量（不输出每个更新的详细日志）
                for node_id, embedding in zip(node_ids, embeddings):
                    update_query = f"""
                    MATCH (n:{label})
                    WHERE elementId(n) = $node_id
                    SET n.embedding = $embedding
                    """
                    self.execute_query(update_query, {
                        'node_id': node_id,
                        'embedding': embedding
                    })

            # 完成后显示简洁信息
            print(f"✅ {label} 节点向量化完成")

        except Exception as e:
            self.log_error(f"向量化 {label} 节点失败: {str(e)}", e)
            raise

    def __del__(self):
        """析构函数，确保连接关闭"""
        try:
            self.close()
        except:
            pass
