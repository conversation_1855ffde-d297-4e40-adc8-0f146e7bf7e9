"""
命令行接口模块
提供命令行参数解析和验证功能
"""

import argparse
from typing import Dict, Any, List, Optional
from pathlib import Path

from config.config import config_service
from utils.data_service import data_service


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数对象
    """
    parser = argparse.ArgumentParser(description="建筑生命全周期安全隐患预测系统 - CrewAI版本")
    
    # 获取可用模式
    system_config = config_service.get_config("system", as_dict=True)
    available_modes = list(system_config["run_mode"]["available_modes"].keys())
    default_mode = system_config["run_mode"]["default_mode"]
    
    parser.add_argument("--mode", type=str, default=default_mode, 
                        choices=available_modes,
                        help="运行模式: full=完整流程, collect=仅数据收集, extract=仅信息提取, evaluate=仅风险评估, report=仅报告生成, check=仅检查系统状态")
    
    # 添加每个模式的帮助信息
    for mode, info in system_config["run_mode"]["available_modes"].items():
        parser.add_argument(f"--{mode}-help", action="store_true",
                            help=f"显示{mode}模式的详细帮助信息")
    
    parser.add_argument("--topic", type=str, default="建筑安全",
                        help="搜索主题（注意：微博爬虫不使用此参数，请在settings.py中配置）")

    parser.add_argument("--location", type=str, default="全部",
                        help="地点（注意：微博爬虫不使用此参数，请在settings.py中配置REGION）")

    parser.add_argument("--time-range", "-t", type=str, default="7d",
                        help="时间范围（注意：微博爬虫不使用此参数，请在settings.py中配置START_DATE和END_DATE）")

    parser.add_argument("--max-posts", "-m", type=int, default=100,
                        help="最大微博数量（注意：微博爬虫不使用此参数，请在settings.py中配置）")

    parser.add_argument("--input", type=str, default=None,
                        help="输入数据文件路径（JSON格式），用于从特定步骤开始运行")

    parser.add_argument("--report-path", "-p", type=str, default=None,
                        help="报告保存路径，默认保存在data/reports目录下")

    parser.add_argument("--keywords", "-k", type=str, nargs="+",
                        help="微博搜索关键词（注意：微博爬虫不使用此参数，请在settings.py中配置KEYWORD_LIST）")
    
    parser.add_argument("--task-id", type=str, default=None,
                        help="任务ID，用于从特定任务开始运行")

    parser.add_argument("--skip-db-check", action="store_true",
                        help="跳过数据库连接检查")
    
    args = parser.parse_args()
    
    # 验证参数
    validate_args(args)
    
    return args


def validate_args(args):
    """
    验证命令行参数
    
    Args:
        args: 解析后的命令行参数
        
    Raises:
        ValueError: 参数验证失败
    """
    mode = args.mode
    system_config = config_service.get_config("system", as_dict=True)
    param_rules = system_config["run_mode"]["param_rules"].get(mode, {})
    
    # 检查必需参数
    required_params = param_rules.get("required", [])
    for param in required_params:
        # 对于collect模式，不强制要求keywords参数
        if mode == "collect" and param == "keywords":
            continue
        
        value = getattr(args, param, None)
        if value is None:
            raise ValueError(f"运行模式 '{mode}' 需要参数 {param}")
            
    # 检查依赖关系
    if mode in system_config["run_mode"]["dependencies"]:
        dependencies = system_config["run_mode"]["dependencies"][mode]
        if not args.input and dependencies:
            deps_str = "、".join(dependencies)
            raise ValueError(f"运行模式 '{mode}' 依赖于{deps_str}的输出，请提供输入文件")
            
    # 检查时间范围格式
    if args.time_range:
        valid_time_ranges = ['1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '7d', '30d', 'all']
        if args.time_range.startswith('custom:'):
            pass  # 自定义时间范围格式，不做验证
        elif args.time_range.endswith('d') and args.time_range[:-1].isdigit():
            pass  # 数字+d格式，如"7d"，不做验证
        elif args.time_range not in valid_time_ranges:
            raise ValueError(f"无效的时间范围格式: {args.time_range}，有效值为: {', '.join(valid_time_ranges)}")
        
    # 检查地点格式
    if args.location:
        if not isinstance(args.location, str):
            raise ValueError(f"地点必须是字符串，当前类型: {type(args.location)}")
        
    # 检查关键词格式
    if args.keywords:
        if not all(isinstance(k, str) for k in args.keywords):
            raise ValueError("所有关键词必须是字符串类型")
            
    # 检查文件路径
    if args.input:
        is_valid, error_msg = data_service.validate_path(args.input, should_exist=True, is_file=True, context="输入文件")
        if not is_valid:
            raise ValueError(error_msg)
        
    if args.report_path:
        report_dir = Path(args.report_path).parent
        is_valid, error_msg = data_service.validate_path(report_dir, should_exist=True, is_dir=True, context="报告目录")
        if not is_valid:
            raise ValueError(error_msg) 