"""
法规检索引擎模块
提供基于向量数据库的法规文档检索功能
"""

from .rag_engine import LawRAGEngine

# 获取引擎实例
engine = LawRAGEngine.get_instance()

# 导出公共函数
def search_laws(query: str, top_k: int = 5) -> str:
    """
    搜索相关法规
    
    Args:
        query: 查询文本
        top_k: 返回的最大结果数量
        
    Returns:
        str: 搜索结果，格式化为Markdown文本
    """
    return engine.search_laws(query, top_k)

def batch_search_laws(query_list: list, top_k: int = 3) -> dict:
    """
    批量搜索相关法规
    
    Args:
        query_list: 查询文本列表
        top_k: 每个查询返回的最大结果数量
        
    Returns:
        dict: 搜索结果字典，键为查询文本，值为搜索结果
    """
    return engine.batch_search_laws(query_list, top_k)

def add_law_document(content: str, document_name: str = None) -> str:
    """
    添加法规文档到向量数据库
    
    Args:
        content: 文档内容
        document_name: 文档名称
        
    Returns:
        str: 添加结果
    """
    return engine.add_law_document(content, document_name)

# 导出工具函数列表，用于CrewAI
law_tools = [
    {
        "name": "search_laws",
        "description": "搜索相关法规文档",
        "function": search_laws
    },
    {
        "name": "batch_search_laws",
        "description": "批量搜索相关法规文档",
        "function": batch_search_laws
    },
    {
        "name": "add_law_document",
        "description": "添加法规文档到向量数据库",
        "function": add_law_document
    }
]

# 导出名称
__all__ = [
    'LawRAGEngine', 'search_laws', 'batch_search_laws', 
    'add_law_document', 'law_tools'
] 
