"""
基础智能体模块
提供所有智能体的基类
"""

# 标准库导入
import json
import logging
import os
import time
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple

# 第三方库导入
from crewai import Agent
from langchain.prompts import MessagesPlaceholder
from langchain.schema import SystemMessage

# 本地应用导入
from config.config import config_service
from utils.data_service import data_service
from utils.exceptions import (
    TaskException, SystemException, ERR_TASK, ERR_SYSTEM,
    safe_execute
)
from utils.logger import LoggerMixin

class BaseAgent(Agent, LoggerMixin):
    """
    基础智能体类
    所有智能体的基类，提供通用功能
    """
    
    def __init__(
        self,
        name: str,
        role: str,
        goal: str,
        backstory: str,
        llm_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False,
        tools: Optional[List] = None,
        **kwargs
    ):
        """初始化基础智能体"""
        # 获取LLM实例
        llm_instance = self._create_llm(llm_config)

        # 调用父类初始化
        Agent.__init__(
            self,
            role=role,
            goal=goal,
            backstory=backstory,
            llm=llm_instance,
            verbose=verbose,
            tools=tools or [],
            **kwargs
        )

        # 在父类初始化完成后设置私有属性
        object.__setattr__(self, '_logger_name', f"agent.{name}")
        object.__setattr__(self, '_agent_name', name)

        self.log_info(f"智能体 {name} 初始化完成")

    def _create_llm(self, llm_config: Optional[Dict[str, Any]] = None):
        """创建LLM实例"""
        if llm_config is None:
            llm_config = config_service.get_config("llm", as_dict=True)

        from tools.llm_client.client import LLMClient
        llm_client = LLMClient(llm_config or {})
        return llm_client.llm

    def call_llm(self, prompt: str) -> str:
        """调用LLM"""
        try:
            response = self.llm.call(prompt)

            if hasattr(response, 'content'):
                return response.content.strip()
            else:
                return str(response).strip()

        except Exception as e:
            self.log_warning(f"LLM调用失败: {e}")
            return ""

    @property
    def name(self) -> str:
        """获取智能体名称"""
        return getattr(self, '_agent_name', 'Unknown Agent')

    def get_path(self, path_name: str) -> Path:
        """获取配置路径"""
        return config_service.get_path(path_name)
    
    @safe_execute
    def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务，子类必须实现"""
        _ = task_input
        raise NotImplementedError("子类必须实现execute方法")

    def extract_structured_data(self, result: Any) -> Dict[str, Any]:
        """从LLM结果中提取结构化数据"""
        try:
            json_data = data_service.extract_json_from_text(str(result))
            if json_data is None:
                raise SystemException("无法从结果中提取JSON数据", code=ERR_SYSTEM)
            return json_data
        except Exception as e:
            self.log_error(f"提取结构化数据失败: {e}")
            raise SystemException(f"提取结构化数据失败: {e}", code=ERR_SYSTEM)
    
    def format_task_result(self, status: str, data: Any = None, error: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """格式化任务结果"""
        result = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "agent": self._agent_name
        }
        
        if data is not None:
            result["data"] = data
            
        if error is not None:
            result["error"] = error
            
        # 添加其他参数
        for key, value in kwargs.items():
            result[key] = value
            
        return result
    
    def log_task_start(self, task_name: str, task_id: Optional[str] = None) -> None:
        """
        记录任务开始
        
        Args:
            task_name: 任务名称
            task_id: 任务ID
        """
        log_msg = f"开始执行任务: {task_name}"
        if task_id:
            log_msg += f" (ID: {task_id})"
        self.log_info(log_msg)
    
    def log_task_end(self, task_name: str, status: str, duration: float, task_id: Optional[str] = None) -> None:
        """
        记录任务结束
        
        Args:
            task_name: 任务名称
            status: 任务状态
            duration: 任务持续时间（秒）
            task_id: 任务ID
        """
        log_msg = f"任务执行完成: {task_name}, 状态: {status}, 耗时: {duration:.2f}秒"
        if task_id:
            log_msg += f" (ID: {task_id})"
        self.log_info(log_msg)
    
    def log_task_error(self, task_name: str, error: Exception, task_id: Optional[str] = None) -> None:
        """
        记录任务错误
        
        Args:
            task_name: 任务名称
            error: 错误信息
            task_id: 任务ID
        """
        log_msg = f"任务执行失败: {task_name}, 错误: {str(error)}"
        if task_id:
            log_msg += f" (ID: {task_id})"
        self.log_error(log_msg)
        self.log_debug(f"错误详情: {traceback.format_exc()}")
    
    def get_task_file_path(self, task_id: str) -> Path:
        """
        获取任务文件路径
        
        Args:
            task_id: 任务ID
            
        Returns:
            Path: 任务文件路径
        """
        return data_service.get_task_path(task_id, "status")
    
    def get_status_file_path(self, task_id: str) -> Path:
        """
        获取状态文件路径

        Args:
            task_id: 任务ID

        Returns:
            Path: 状态文件路径
        """
        return self.get_path("status_dir") / f"{task_id}.json"
